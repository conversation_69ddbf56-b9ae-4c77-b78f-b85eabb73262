# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
android.enableJetifier=true
android.useAndroidX=true
android.useDeprecatedNdk=true
org.gradle.jvmargs=-Xmx4608m

# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
org.gradle.parallel=true
# ?????????Android Studio 3.0??debug apk?manifest??application??????? android:testOnly="true"???
# ??????IDE???Run???apk??????????adb install -t ????
# ?gradle.properties ????????????????
# android.injected.testOnly=false
android.injected.testOnly=false

# App size optimization settings
# Enable R8 full mode for better optimization
android.enableR8.fullMode=true

# Enable resource shrinking
android.enableResourceOptimizations=true

# Enable build cache for faster builds
org.gradle.caching=true

# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true