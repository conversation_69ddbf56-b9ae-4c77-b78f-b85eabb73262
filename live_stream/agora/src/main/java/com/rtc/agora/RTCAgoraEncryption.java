package com.rtc.agora;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Keep
public class RTCAgoraEncryption implements Serializable {
    @SerializedName("encryption_key")
    private String encryptionKey;
    @SerializedName("encryption_salt")
    private String encryptionSalt;
    @SerializedName("encryption_mode")
    private String encryptionMode;

    public String getEncryptionKey() {
        return encryptionKey;
    }

    public String getEncryptionSalt() {
        return encryptionSalt;
    }

    public String getEncryptionMode() {
        return encryptionMode;
    }
}
