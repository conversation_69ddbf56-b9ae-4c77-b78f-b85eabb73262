<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/skin_color_white"
    tools:context=".activity.DsBridgeWebviewActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:id="@+id/top_view"
            android:layout_width="match_parent"
            android:layout_height="0dp" />

        <include layout="@layout/view_title" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <wendu.dsbridge.DWebView
            android:id="@+id/webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:scrollbars="none" />

        <com.hjq.shape.layout.ShapeConstraintLayout
            android:id="@+id/ll_menu"
            android:layout_width="75dp"
            android:layout_height="28dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp10"
            android:gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            android:orientation="horizontal"
            app:shape_radius="@dimen/dp20"
            app:shape_strokeColor="@color/white"
            app:shape_strokeSize="1dp">

            <ImageView
                android:id="@+id/iv_refresh"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:src="@mipmap/ic_refresh"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/vDivide"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/vDivide"
                android:layout_width="1dp"
                android:layout_height="16dp"
                android:background="@color/skin_color_white_20"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_circle_close"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:src="@mipmap/ic_cicle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/vDivide"
                app:layout_constraintTop_toTopOf="parent" />
        </com.hjq.shape.layout.ShapeConstraintLayout>

    </RelativeLayout>

</LinearLayout>