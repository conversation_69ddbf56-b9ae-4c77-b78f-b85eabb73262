<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    style="@style/SelectableTextView"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:layout_marginHorizontal="@dimen/dp20"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/pinyin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:textColor="#88a4c5"
        android:textSize="11sp" />

    <TextView
        android:id="@+id/tv_city"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:gravity="left"
        android:textColor="@color/c_33"
        android:textSize="13sp"
        tools:text="@tools:sample/cities" />
</LinearLayout>
