<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="280dp"
              android:layout_height="wrap_content"
              android:background="@drawable/bg_dialog"
              android:gravity="center_horizontal"
              android:orientation="vertical"
    >

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:layout_marginTop="15dp"
        android:gravity="center_horizontal"
        android:text="@string/dialog_tip"
        android:textColor="@color/textColor"
        android:textSize="15sp"
        />

    <TextView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:textColor="@color/textColor"
        android:textSize="14sp"
        android:gravity="center_horizontal"
        />

    <View
        style="@style/line2"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:layout_height="40dp">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="40dp"
            android:gravity="center"
            android:text="@string/cancel"
            android:visibility="gone"
            android:textColor="@color/textColor2"
            android:textSize="14sp"
            />
        <View
            android:id="@+id/line1"
            android:visibility="gone"
            android:layout_width="1dp"
            android:background="@color/grey2"
            android:layout_height="match_parent"/>
        <TextView
            android:id="@+id/btn_confirm"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="40dp"
            android:gravity="center"
            android:text="@string/confirm"
            android:textColor="@color/global"
            android:textSize="14sp"
            />
    </LinearLayout>

</LinearLayout>