<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@android:color/transparent"
    android:layout_width="300dp"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="36dp"
        android:background="@drawable/skin_shape_bg_common_input_popup"
        android:paddingHorizontal="13dp"
        android:paddingBottom="14dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/image">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="27dp"
            android:gravity="center"
            android:text="提示"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:gravity="center"
            android:text="余额不足, 是否前往充值"
            android:textColor="#bf333333"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvMsg">

            <TextView
                android:id="@+id/tvCancel"
                android:layout_width="136dp"
                android:layout_height="39dp"
                android:layout_weight="1"
                android:background="@drawable/skin_shape_bg_common_input_cancel"
                android:gravity="center"
                android:text="取消"
                android:textColor="#11cfff"
                android:textSize="13sp"
                app:shape_radius="@dimen/dp22"
                app:shape_strokeColor="@color/c_fa"
                app:shape_strokeSize="@dimen/dp1" />

            <View
                android:id="@+id/spaceView"
                android:layout_width="14dp"
                android:layout_height="39dp" />

            <TextView
                android:id="@+id/tvRecharge"
                android:layout_width="136dp"
                android:layout_height="39dp"
                android:layout_weight="1"
                android:background="@drawable/skin_shape_bg_common_input_sure"
                android:gravity="center"
                android:text="去充值"
                android:textColor="@color/white"
                android:textSize="13sp"
                app:shape_radius="@dimen/dp22"
                app:shape_solidGradientEndColor="@color/c_f9"
                app:shape_solidGradientStartColor="@color/c_ff82" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/image"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@drawable/skin_icon_notice_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>