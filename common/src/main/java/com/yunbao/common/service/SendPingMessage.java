package com.yunbao.common.service;

import java.io.Serializable;

/**
 * 心跳检测消息
 */
public class SendPingMessage implements Serializable {

//    "type": 0, // Ping, 必须是 number 类型
//            "client_id": "连接成功后返回",
//            "data": {}, // 必须是 object 类型，不允许空字符串

    private int event_type;
    private String client_id;
    private String unique_key;

    public String getUnique_key() {
        return unique_key;
    }
    public int getEvent_type() {
        return event_type;
    }

    public void setEvent_type(int event_type) {
        this.event_type = event_type;
    }

    public String getClient_id() {
        return client_id;
    }

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public void setUnique_key(String unique_key) {
        this.unique_key = unique_key;
    }

    @Override
    public String toString() {
        return "PingMessage{" +
                "type=" + event_type +
                ", client_id='" + client_id + '\'' +
                '}';
    }
}
