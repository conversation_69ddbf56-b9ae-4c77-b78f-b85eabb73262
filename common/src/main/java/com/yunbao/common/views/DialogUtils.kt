package com.yunbao.common.views

import androidx.fragment.app.FragmentManager

object DialogUtils {

    fun showInputDialog(
        fragmentManager: FragmentManager,
        title: String = "",
        msg: String = "",
        hint: String = "",
        content: String = "",
        callback: CommonInputDialog.CommonInputCallback? = null,
        onlyNum: Boolean = true,
        maxLength: Int = -1
    ) {
        val inputDialog =
            CommonInputDialog(title, msg, hint, content, callback, onlyNum, maxLength)
        inputDialog.let {
            inputDialog.showAllowingStateLoss(fragmentManager, CommonInputDialog.TAG)
        }
    }
    fun showChipsInputDialog(
        fragmentManager: FragmentManager,
        title: String = "",
        msg: String = "",
        hint: String = "",
        content: String = "",
        callback: CommonInputDialog.CommonInputCallback? = null,
        exitAmountList: List<Int> = emptyList(),
        onlyNum: Boolean = true
    ) {
        val inputDialog =
            ChipsInputDialog(title, msg, hint, content, callback, exitAmountList, onlyNum)
        inputDialog.let {
            inputDialog.showAllowingStateLoss(fragmentManager, CommonInputDialog.TAG)
        }
    }
}