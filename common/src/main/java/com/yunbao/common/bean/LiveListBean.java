package com.yunbao.common.bean;

import android.annotation.SuppressLint;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * 直播列表实体
 */
public class LiveListBean implements Serializable {


    private int count;
    private List<ListBean> list;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class ListBean implements Serializable {
        public static DiffUtil.ItemCallback<ListBean> diffUtil = new DiffUtil.ItemCallback<ListBean>() {
            @Override
            public boolean areItemsTheSame(@NonNull ListBean oldItem, @NonNull ListBean newItem) {
                return oldItem.id == newItem.id;
            }

            @SuppressLint("DiffUtilEquals")
            @Override
            public boolean areContentsTheSame(@NonNull ListBean oldItem, @NonNull ListBean newItem) {
                return oldItem.equals(newItem);
            }
        };

        public ListBean() {
        }

        public ListBean(int id) {
            this.id = id;
        }

        private int id;
        private int game_id;
        private String game_name;
        private String title;
        private String thumb;
        private int mode;
        private int cost;
        private String city;
        private int look_num;
        private int lovense;
        //0 未pk ，1：pk
        @SerializedName("pk_status")
        private int pkStatus;
        //主播昵称
        @SerializedName("anchor_nickname")
        private String anchorNickname;
        //主播头像
        @SerializedName("anchor_avatar")
        private String anchorAvatar;

        public int getId() {
            return id;
        }

        public boolean isLovenseEnabled() {
            return lovense == 1;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getGame_id() {
            return game_id;
        }

        public void setGame_id(int game_id) {
            this.game_id = game_id;
        }

        public String getGame_name() {
            return game_name;
        }

        public void setGame_name(String game_name) {
            this.game_name = game_name;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getThumb() {
            return thumb;
        }

        public void setThumb(String thumb) {
            this.thumb = thumb;
        }

        public int getMode() {
            return mode;
        }

        public void setMode(int mode) {
            this.mode = mode;
        }

        public int getCost() {
            return cost;
        }

        public void setCost(int cost) {
            this.cost = cost;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public int getLook_num() {
            return look_num;
        }

        public void setLook_num(int look_num) {
            this.look_num = look_num;
        }

        public int getLovense() {
            return lovense;
        }

        public void setLovense(int lovense) {
            this.lovense = lovense;
        }

        public int getPkStatus() {
            return pkStatus;
        }

        public void setPkStatus(int pkStatus) {
            this.pkStatus = pkStatus;
        }

        public String getAnchorNickname() {
            return anchorNickname;
        }

        public void setAnchorNickname(String anchorNickname) {
            this.anchorNickname = anchorNickname;
        }

        public String getAnchorAvatar() {
            return anchorAvatar;
        }

        public void setAnchorAvatar(String anchorAvatar) {
            this.anchorAvatar = anchorAvatar;
        }
    }
}
