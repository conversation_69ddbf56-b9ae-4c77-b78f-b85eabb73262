package com.yunbao.common.bean;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Keep
public class LiveTencentInfoBean implements Serializable {
    @SerializedName("trtc")
    private TencentToken tencentToken;
    @SerializedName("trtc_host_uid")
    private int trtcHostUid;
    @SerializedName("is_interactive")
    private boolean isInteractive;

    @SerializedName("pull_uri")
    private String pullUri;

    public String getPullUri() {
        return pullUri;
    }

    private LiveInfoBean.RoomInteractiveInfoBean interactiveInfo;

    public TencentToken getTencentToken() {
        return tencentToken;
    }

    public void setTencentToken(TencentToken tencentToken) {
        this.tencentToken = tencentToken;
    }

    public int getTrtcHostUid() {
        return trtcHostUid;
    }

    public void setTrtcHostUid(int trtcHostUid) {
        this.trtcHostUid = trtcHostUid;
    }

    public boolean isInteractive() {
        return isInteractive;
    }

    public void setInteractive(boolean interactive) {
        this.isInteractive = interactive;
    }

    public LiveInfoBean.RoomInteractiveInfoBean getInteractiveInfo() {
        return interactiveInfo;
    }

    public void transform() {
        if (isInteractive) {
            interactiveInfo = new LiveInfoBean.RoomInteractiveInfoBean(
                    trtcHostUid,
                    tencentToken
            );
        }
    }
}
