package com.yunbao.common.bean

import com.yunbao.common.Constants
import com.yunbao.common.LiveApp
import java.io.Serializable

/**
 * 直播间消息
 */
open class LiveMessage(
    var messageType: Int = Constants.MESSAGE_TYPE_CHAT,
    var userId: Int = 0,
    var userName: String? = "",
    var message: String? = "",
    var icon: String? = "",
    var anchorId: Int = 0,
    var anchorName: String? = "",
    var gameId: Int = 0,
    var gameName: String? = "",
    //投注总金额
    var amount: String? = "",
    var defaultGameId: Int = 0,
    var roomId: Int = 0,
    var additionalInfo: Any? = null,
    var nobleName: String? = "",
    var url: String? = "",
    var giftKey: String = "",
    var giftName: String = "",
    var giftNum: Int = 0,
    var userInfo: UserInfoBean? = null,
    var id: Int = 0,
    var mId:String = "",
    var nickName : String ="",
    var uuid : String? = "",
    var isRoomManager : Int = 0,
    var isKick : Boolean = false,
    var isNoSpeak : Boolean = false,
    var rId : Int = 0,
    var level: Int = 0,
    //用户进入使用--进入时间
    var enterTime: Long = 0L
) : Serializable {
    val isOwn: Boolean
        get() = userInfo?.id == LiveApp.userId
}
