package com.yunbao.common.bean

import com.google.gson.annotations.SerializedName
import com.yunbao.common.Constants
import com.yunbao.common.utils.DataUtil
import com.yunbao.common.utils.DataUtil.getNobleFrameSvga
import java.io.Serializable

/**
 * 个人信息实体
 */
data class UserInfoBean(
    @SerializedName(value = "id", alternate = ["user_id"])
    var id:Int = 0,
    var phone: String? = null,
    var nickname: String? = null,
    @SerializedName("avatar", alternate = ["avator"])
    var avatar: String? = null,
    var gender:Int = 0,
    var sex: String? = null,

    @SerializedName("sign_name")
    var signName: String? = null,
    var hometown: String? = null,
    var occupation: String? = null,
    var age: String? = null,
    var feeling: String? = null,
    var level:Int = 0,

    @SerializedName("level_icon")
    var levelIcon: String? = null,
    var birthday: String? = null,

    @SerializedName("is_set_payment_password")
    var isSetPaymentPassword:Int = 0, //0 未设置 1 已设置

    @SerializedName("fons_count")
    var fansCount:Int = 0,

    @SerializedName("facus_count")
    var followCount:Int = 0,

    @SerializedName("send_num")
    var sendCount:Int = 0,

    @SerializedName("head_frame")
    var headFrame: String? = null,

    @SerializedName("noble_icon")
    var nobleIcon: String? = null,

    @SerializedName("guard_icon")
    var guardIcon: String? = null,

    //入场隐身 1（隐身）0（不隐身）
    @SerializedName("entrance_msg_stealth")
    var enterStealth:Int = 0,

    //榜单隐身 1（隐身）0（不隐身）
    @SerializedName("rank_stealth")
    var rankStealth:Int = 0,

    @SerializedName("is_bind")
    var hasBindPhone:Int = 0,

    @SerializedName("nice_num")
    var niceNum: String? = "",

    @SerializedName("country_code")
    var countryCode: String? = null,
    @SerializedName("i_m_room_manager")
    var isImRoomManager: Boolean = false,

    @SerializedName("is_room_manager")
    var isRoomManager: Boolean = false,

    @SerializedName("is_nospeak")
    var isNoSpeak: Boolean = false,

    @SerializedName("is_kick")
    var isKick: Boolean = false,

    //爵位名称 骑士  子爵   伯爵   侯爵  公爵  国王
    @SerializedName("noble_name")
    var nobleName: String? = null,

    var uid: String? = "",
    var code: String? = "",
    var wechat: String? = "",
    var sign: String? = "",
    @SerializedName("live_divide")
    var liveDivide: String? = "",
    @SerializedName("game_divide")
    var gameDivide: String? = "",
    //1/true隐身 0/false不隐身
    var hidden: Any? = null,
    //0未直播 1直播中
    @SerializedName("islive")
    val isLive: Int = 0,
    //用户进入是否已经展示
    var hasEnterShow: Boolean = false,
    //用户金光 tip
    var enterTip: String = "进入直播间",
    var showGuardSvga: Boolean = false,
    var guardType: Int = -1,
    //用户进入使用--进入时间
    var enterTime: Long = 0L,
    @SerializedName("subchannel_key")
    val channel: String? = "",
    //主
    val main: String? = "",
    //备
    val backup: String? = "",
    //备用key
    var key: String? = "",
    var uuid: String? = "",
    @SerializedName("use_kk")
    val useKk: Int = 0,
    //1:byte,2:kk,3:anquan
    @SerializedName("dun_type")
    val dunType: Int = -1,
    //0:非，1:域名优先
    @SerializedName("do")
    val isDomainFirst: Int = 0
): Serializable {
    val svgaPath: String
        get() = DataUtil.getNobleSvga(nobleName, showGuardSvga, guardType)
    val svgaNameKey: String
        get() = "01"
    val svgaLevelKey: String
        get() = "04"
    val svgaTipKey: String
        get() = "02"
    val svgaAvatarKey: String
        get() = "03"
    val knighthood: String
        get() = getNobleFrameSvga(nobleName)
    val useDunType: Int
        get() = if(dunType == -1 && useKk == 1) Constants.DUN_TYPE_KK else dunType

}
