package com.yunbao.common.utils

import com.yunbao.common.interfaces.BaseWsCallback
import java.util.WeakHashMap

object CallbackManager {
    private val CALLBACKS: WeakHashMap<Any, BaseWsCallback> = WeakHashMap()

    /**
     * 静态类部类实现单例
     */
    private object Holder {
        val INSTANCE = CallbackManager
    }

    fun getInstance(): CallbackManager {
        return Holder.INSTANCE
    }

    /**
     * 添加全局回调
     * @param tag
     * @param callback
     * @return
     */
    fun addCallback(tag: Any, callback: BaseWsCallback): CallbackManager {
        CALLBACKS[tag] = callback
        return this
    }

    /**
     * 获取指定回调的接口
     * @param tag
     * @return
     */
    fun getCallback(tag: Any): BaseWsCallback? {
        return CALLBACKS[tag]
    }

    /**
     * 移除
     */
    fun removeCallback(tag: Any) {
        CALLBACKS.remove(tag)
    }
}