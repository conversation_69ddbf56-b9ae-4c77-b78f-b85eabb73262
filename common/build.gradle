plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
        buildConfigField 'String', 'ZIFY_DM01', "\"zmxnj.com\""
        buildConfigField 'String', 'ZIFY_DM02', "\"eufko.com\""
        buildConfigField 'String', 'ZIFY_DM03', "\"vwfyc.com\""
    }
    aaptOptions {
        // Enable cruncher for better PNG optimization
        cruncherEnabled = true
        useNewCruncher = true
    }
    buildTypes {
        debug {
            buildConfigField('String', 'VERSION_NAME', '"' + defaultConfig.versionName + '"')   // root version name
            buildConfigField "String", "AES_KEY", "\"BD8r6FMu8tVhPDBo\""
            buildConfigField "String", "AES_VI", "\"I7cvKphClAJrigOL\""
            buildConfigField "String", "ALI_ACCOUNT_ID", "\"****************\""
            buildConfigField "String", "IMG_AES_KEY", "\"iTaOUWPY74I3jKQe\""
            buildConfigField "String", "IMG_AES_VI", "\"mtmgmaXtGeMfjLQl\""
            buildConfigField "String", "DOMAIN_UID", "\"144274\""
            buildConfigField "String", "DOMAIN_USER_KEY", "\"decc504b7f824ead9e8ff020c90410b9\""
            buildConfigField "String", "DOMAIN_AK", "\"144274_26537028761109504\""
        }
        release {
            minifyEnabled false
            buildConfigField('String', 'VERSION_NAME', '"' + defaultConfig.versionName + '"')   // root version name
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "String", "AES_KEY", "\"BD8r6FMu8tVhPDBo\""
            buildConfigField "String", "AES_VI", "\"I7cvKphClAJrigOL\""
            buildConfigField "String", "ALI_ACCOUNT_ID", "\"****************\""
            buildConfigField "String", "IMG_AES_KEY", "\"iTaOUWPY74I3jKQe\""
            buildConfigField "String", "IMG_AES_VI", "\"mtmgmaXtGeMfjLQl\""
            buildConfigField "String", "DOMAIN_UID", "\"144274\""
            buildConfigField "String", "DOMAIN_USER_KEY", "\"decc504b7f824ead9e8ff020c90410b9\""
            buildConfigField "String", "DOMAIN_AK", "\"144274_26537028761109504\""
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding true
    }
}

//kapt {
//    generateStubs = true
//    arguments {
//        arg("AROUTER_MODULE_NAME", project.getName())
//    }
//}
repositories {
    flatDir {
        dirs 'libs', '../libs'
    }
}
dependencies {
    api fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    api rootProject.ext.dependencies["appcompat-v7"]
    api rootProject.ext.dependencies["recyclerview-v7"]
    api rootProject.ext.dependencies["cardview-v7"]
    api rootProject.ext.dependencies["design"]
    api rootProject.ext.dependencies["multidex"]
    //    //okHttp网络请求
    api rootProject.ext.dependencies["okhttp"]
    api rootProject.ext.dependencies["ohhttp-logging-interceptor"]

    //fastJson解析json
    api rootProject.ext.dependencies["fastjson"]
    api rootProject.ext.dependencies["fastjson_kotlin"]

    //eventbus事件总线
    api rootProject.ext.dependencies["eventbus"]

    //使用Glide加载图片
    api rootProject.ext.dependencies["glide"]
    kapt 'com.github.bumptech.glide:compiler:4.14.2'

    //一个基于Glide的transformation库,拥有裁剪,着色,模糊,滤镜等多种转换效果
    api rootProject.ext.dependencies["glide-transformations"]

    //带圆角，边框的的ImageView
    api rootProject.ext.dependencies["roundedimageview"]

    //gif库，播放礼物gif用
    api rootProject.ext.dependencies["gif-drawable"]

    //svga播放器
    api rootProject.ext.dependencies["SVGAPlayer"]
    //svga扩展
    api 'com.github.YvesCheung:SVGAGlidePlugin:4.13.3'

    //下拉刷新上拉加载
    api 'io.github.scwang90:refresh-layout-kernel:2.1.0'
    //经典加载
    api 'io.github.scwang90:refresh-footer-classics:2.1.0'

    //ViewPager指示器
    api rootProject.ext.dependencies["magicIndicator"]

    api 'com.lzy.net:okgo:3.0.4'

    //阿里 ARouter
    implementation rootProject.ext.dependencies["arouter"]
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.core:core-ktx:1.8.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    kapt rootProject.ext.dependencies["arouter-compiler"]

    //轮播图
    api rootProject.ext.dependencies["banner"]

    //compile 'com.squareup.leakcanary:leakcanary-android:1.6.3'
    api rootProject.ext.dependencies["androidx.constraintlayout"]

    api rootProject.ext.dependencies["ccp"]
    //log
    api rootProject.ext.dependencies["xlog"]
    api 'com.elvishew:xlog-libcat:1.0.0'
    // 安卓工具类库
    api rootProject.ext.dependencies["utilcode"]

    //>>>>>>>>>>anH start
    //网络请求框架--start
    api 'com.github.leavesCZY:ReactiveHttp:1.1.3'
    api 'com.squareup.retrofit2:retrofit:2.9.0'
    api 'com.squareup.retrofit2:converter-gson:2.9.0'
    //kotlin协程
    api 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    api "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4"
    //lifecycleScope
    api 'androidx.fragment:fragment-ktx:1.5.1'
    api 'androidx.lifecycle:lifecycle-livedata-ktx:2.5.1'
    api 'androidx.lifecycle:lifecycle-runtime-ktx:2.5.1'
    api 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1'
    //拦截器
//    debugApi 'com.github.leavesC.Monitor:monitor:1.2.1'
    api 'com.github.leavesC.Monitor:monitor-no-op:1.1.3'
    //日志输出
    api 'com.squareup.okhttp3:logging-interceptor:5.0.0-alpha.3'
    //网络请求框架--end
    //沉浸式状态栏
    api 'com.geyifeng.immersionbar:immersionbar:3.2.2'
    // kotlin扩展（可选）
    api 'com.geyifeng.immersionbar:immersionbar-ktx:3.2.2'
    //数据适配器
    api 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.7'
    //验证码、密码输入框
    api 'com.open.keeplearn:SplitEditTextView:1.2.3'
    //>>>>>>>>>>anH end

    //XPopup
    api 'com.github.li-xiaojun:XPopup:2.9.19'
    //弹框
//    api("com.kongzue.dialogx:DialogX:0.0.49")
    api("com.github.kongzue.DialogX:DialogX:0.0.50.beta17.1")
    //日期选择
    //自定义弹窗
    api 'com.github.gzu-liyujiang.AndroidPicker:Common:4.1.8'
    //滚轮控件
    api 'com.github.gzu-liyujiang.AndroidPicker:WheelView:4.1.8'
    //单项/数字、二三级联动、日期/时间等滚轮选择器
    api 'com.github.gzu-liyujiang.AndroidPicker:WheelPicker:4.1.8'
    //省市区地址选择器
    api 'com.github.gzu-liyujiang.AndroidPicker:AddressPicker:4.1.8'
    //seekbar
//    api 'com.github.warkiz.widget:indicatorseekbar:2.1.2'
    api project(':indicatorseekbar')
    //模拟器检测
    api 'io.github.happylishang:antifake:1.7.0'
    //富文本
    api 'com.zzhoujay.richtext:richtext:3.0.8'
    api 'com.zzhoujay:html:1.0.2'

    implementation 'cn.hutool:hutool-crypto:5.8.6'

    implementation 'io.reactivex.rxjava2:rxjava:2.2.19'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    implementation 'com.trello.rxlifecycle2:rxlifecycle-android-lifecycle:2.2.2'

    //动画效果
    api 'com.airbnb.android:lottie:5.2.0'
    api 'io.github.h07000223:flycoTabLayout:3.0.0'
    //图库选择PictureSelector
    api('io.github.lucksiege:pictureselector:v3.11.2', {
        exclude group: 'androidx.activity', module: 'activity'
    })
    //压缩
    api 'io.github.lucksiege:compress:v3.11.2'
    //裁剪
    api 'io.github.lucksiege:ucrop:v3.11.2'
    api 'com.google.code.gson:gson:2.10.1'

    api "com.github.hackware1993:MagicIndicator:1.7.0"
    //权限请求
    api 'com.github.getActivity:XXPermissions:18.6'

    api 'io.github.youth5201314:banner:2.2.2'
    api 'com.github.bingoogolapple.BGAQRCode-Android:zxing:1.3.8'
    //流式布局
    api 'com.google.android.flexbox:flexbox:3.0.0'
    //三方webview
    api 'com.github.delight-im:Android-AdvancedWebView:v3.2.1'
    api 'com.liulishuo.filedownloader:library:1.7.7'
    implementation 'org.jsoup:jsoup:1.14.3'
    api 'com.github.getActivity:ShapeView:9.0'

    //coil
    api("io.coil-kt:coil:2.6.0")
    api("io.coil-kt:coil-gif:2.6.0")
//    api("io.coil-kt:coil-svg:2.6.0")

    //本地数据
    api("androidx.datastore:datastore-preferences:1.1.1")

    api("com.github.liangjingkanji:BRV:1.5.8")
    api("io.github.scwang90:refresh-header-classics:2.1.0")
    //富文本
    api("com.github.liangjingkanji:spannable:1.2.7")

    api 'com.github.getActivity:EasyHttp:12.8'
    api 'com.github.getActivity:GsonFactory:9.6'

    api project(':horizontalgridpage')
    //队列
    api project(':tasklibrary')
    //弹幕
    api project(':Muti-Barrage')
    //皮肤
    api project(':skin')
    //消息
    api project(':liveMessage')
    api project(':live_stream')
    //dsbridge
    api project(':dsbridge')
    //观测云
    //添加 SDK 的依赖
    api 'com.cloudcare.ft.mobile.sdk.tracker.agent:ft-sdk:1.7.0-alpha16'
    //捕获 native 层崩溃信息的依赖，需要配合 ft-sdk 使用不能单独使用
    api 'com.cloudcare.ft.mobile.sdk.tracker.agent:ft-native:1.1.1'
    //需要开启 session replay 功能
    api 'com.cloudcare.ft.mobile.sdk.tracker.agent:ft-session-replay:0.1.1-alpha01'
    //需要支持 session replay 需要支持 material 组件
    api 'com.cloudcare.ft.mobile.sdk.tracker.agent:ft-session-replay-material:0.1.1-alpha01'
}

repositories {
    maven { url 'https://jitpack.io' }
    mavenCentral()
}
