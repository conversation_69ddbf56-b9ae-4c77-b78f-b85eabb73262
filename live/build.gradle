plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
//        ndk {
//            abiFilters "armeabi-v7a","arm64-v8a"
//        }
    }
    aaptOptions {
        // Enable cruncher for better PNG optimization
        cruncherEnabled = true
        // useNewCruncher is deprecated and always enabled now
        noCompress "wav,mp3"  //表示不让aapt压缩的文件后缀
    }
    buildTypes {
        release {
            minifyEnabled true
            // Note: shrinkResources not available for library modules
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}
repositories {
    flatDir {
        dirs 'libs','../libs'
    }
}



dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    //socket.io
//    api('io.socket:socket.io-client:1.0.0') {
//        exclude group: 'org.json', module: 'json'
//    }
    //金山播放和推流SDK
//    api 'com.ksyun.media:libksylive-java:3.0.4'
//    api 'com.ksyun.media:libksylive-armv7a:3.0.4'
//    api 'com.ksyun.media:libksylive-arm64:3.0.4'
//    api 'com.ksyun.media:libksylive-x86:3.0.4'
    //common
    api project(':common')
    //游戏
//    api project(':game')
}
