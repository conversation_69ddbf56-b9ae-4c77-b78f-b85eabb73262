<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.ViUserNoticeActivity">

    <com.hjq.bar.TitleBar
        android:id="@+id/titleBar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        style="@style/style_title_bar"
        app:title="@string/vi_user_notice" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar"
        app:layout_constraintBottom_toBottomOf="parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivImg1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                tools:src="@drawable/img_vi_user_notice_1" />

            <ImageView
                android:id="@+id/ivImg2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                tools:src="@drawable/img_vi_user_notice_2" />

            <ImageView
                android:id="@+id/ivImg3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                tools:src="@drawable/img_vi_user_notice_3" />

            <ImageView
                android:id="@+id/ivImg4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                tools:src="@drawable/img_vi_user_notice_4" />

            <ImageView
                android:id="@+id/ivImg5"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                tools:src="@drawable/img_vi_user_notice_5" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <com.yunbao.common.widget.MoveFrameLayout
        android:id="@+id/moveFl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_110"
        app:IsAttach="true"
        app:IsDrag="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/ivService"
            android:layout_width="@dimen/dp_72"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_9"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            android:src="@drawable/icon_vi_customer_service" />

    </com.yunbao.common.widget.MoveFrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>