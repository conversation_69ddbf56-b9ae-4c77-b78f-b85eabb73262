<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_20"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        android:scaleType="fitCenter"
        tools:src="@drawable/selector_vi_tab_buy" />

    <TextView
        android:id="@+id/tvName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:textColor="@color/skin_color_gray_black_select"
        android:textSize="@dimen/sp_12"
        tools:text="@string/app_name" />
</LinearLayout>