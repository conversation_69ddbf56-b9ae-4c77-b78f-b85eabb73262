<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.ViBillActivity">

    <com.hjq.bar.TitleBar
        android:id="@+id/titleBar"
        style="@style/style_title_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/vi_bill" />

    <net.lucode.hackware.magicindicator.MagicIndicator
        android:id="@+id/magicIndicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_45"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/magicIndicator" />

    <com.yunbao.common.widget.MoveFrameLayout
        android:id="@+id/moveFl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_110"
        app:IsAttach="true"
        app:IsDrag="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/ivService"
            android:layout_width="@dimen/dp_72"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_9"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            android:src="@drawable/icon_vi_customer_service" />

    </com.yunbao.common.widget.MoveFrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>