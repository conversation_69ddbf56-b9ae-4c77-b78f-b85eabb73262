<resources>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="http_token_error">登录失效，请重新登录</string>
    <string name="http_data_explain_error">数据解析异常，请稍后</string>
    <string name="http_server_out_time">服务器请求超时，请稍后再试</string>
    <string name="http_network_error">请求失败，请检查网络设置</string>
    <string name="http_response_error">服务器响应异常，请稍后再试，响应码：%d，响应信息：%s, 请求地址：%s</string>
    <string name="http_server_error">服务器连接异常，请稍后再试</string>
    <string name="http_request_cancel">请求被中断，请重试</string>
    <string name="http_response_null_body">服务器数据返回异常，请稍后再试</string>
    <string name="http_response_md5_error">文件 md5 校验失败</string>
    <string name="vi_init_failure">初始化失败，请重新进入VI币</string>
    <string name="vi_token_error">Vi授权失败</string>
    <string name="vi_title_mine">Vi币商城</string>
    <string name="vi_title_buy">我要买</string>
    <string name="vi_title_sell">我要卖</string>
    <string name="vi_title_my_order">我的订单</string>
    <string name="vi_title_account_info">账户信息</string>
    <string name="vi_title_order_detail">订单详情</string>
    <string name="vi_title_place_an_order">下单</string>
    <string name="vi_title_pending_order_detail">挂单详情</string>
    <string name="vi_title_psw_reset">找回支付密码</string>
    <string name="vi_title_psw_change">修改支付密码</string>
    <string name="vi_balance">帐户余额(Vi)</string>
    <string name="vi_unbound">未绑定</string>
    <string name="vi_bound">已绑定</string>
    <string name="vi_unknown">未实名</string>
    <string name="vi_already_relname">已实名</string>
    <string name="vi_excellent_credit">信用极好</string>
    <string name="vi_cny_ph">≈%1$s CNY</string>
    <string name="vi_verified">实名认证</string>
    <string name="vi_verified_success">实名认证成功</string>
    <string name="vi_front">人像面</string>
    <string name="vi_back">国徽面</string>
    <string name="vi_click_upload_id">点击上传身份证原件人像面</string>
    <string name="vi_verified_tip_1">为了给用户构造健康的交易环境，请您先实名认证</string>
    <string name="vi_verified_tip_2"> 实名后不可更改，只能使用实名的资金账户收付款</string>
    <string name="vi_face_verified_failure">人脸识别失败，请重试</string>
    <string name="vi_receive_payment">收付款</string>
    <string name="vi_receive_payment2">收付款方式</string>
    <string name="vi_payment_method">支付方式</string>
    <string name="vi_interval">区间</string>
    <string name="vi_amount">数量</string>
    <string name="vi_menu">菜单</string>
    <string name="vi_psw">支付密码</string>
    <string name="vi_bill">我的账单</string>
    <string name="vi_credit_rating">信用等级</string>
    <string name="vi_buying_tutorial">买入教程</string>
    <string name="vi_sell_tutorial">卖出教程</string>
    <string name="vi_user_notice">用户须知</string>
    <string name="vi_pay">支付</string>
    <string name="vi_complaints">我的申诉</string>
    <string name="vi_setting">设置</string>
    <string name="vi_share">分享</string>
    <string name="vi_send_failure">发送失败</string>
    <string name="vi_deliver_failure">放币失败，请重试</string>
    <string name="vi_confirm_pay_failure">确认付款失败，请重试</string>
    <string name="vi_place_order_failure">下单失败，请重试</string>
    <string name="vi_load_error">数据加载失败，请下拉刷新</string>
    <string name="vi_credit_rating_very_bad">信用极差</string>
    <string name="vi_credit_rating_poor">信用较差</string>
    <string name="vi_credit_rating_medium">信用中等</string>
    <string name="vi_credit_rating_good">信用良好</string>
    <string name="vi_credit_rating_excellent">信用优秀</string>
    <string name="vi_credit_rating_perfect">信用极好</string>
    <string name="vi_credit_rating_tip_title">什么是信用分？</string>
    <string name="vi_credit_rating_tip_1">信用分是对个人的综合评分，主要由以下信息评估得出</string>
    <string name="vi_credit_rating_tip_2">&amp; 身份认证：完善实名信息以及支付信息\n\n&amp; 交易行为：交易时按时完成，未超过处理订单，较高的交易次数也是有助于信用分评估\n\n&amp; 守约记录：不同场景的履约记录，逾期交易或频繁关闭订单将严重影响信用分</string>
    <string name="usdt_exchange_vi">USDT兑换Vi</string>
    <string name="usdt_exchange_vi_rate_ph">1 USDT ≈ %1$s Vi</string>
    <string name="vi_order_quantity_ph">成单量%1$s</string>
    <string name="vi_quantity_ph">数量 %1$s Vi</string>
    <string name="vi_limits_ph">限额 %1$s Vi</string>
    <string name="vi_unit">¥ / 单价</string>
    <string name="vi_price_unit">¥</string>
    <string name="vi_cancel">取消</string>
    <string name="vi_cancelled">已取消</string>
    <string name="vi_buy_ph">买入 %1$s Vi</string>
    <string name="vi_cancel_failure">取消失败</string>
    <string name="vi_order_reject_failure">订单拒绝失败，请重试</string>
    <string name="vi_order_confirm_failure">订单确认失败，请重试</string>
    <string name="vi_order_state_wait_confirm">等待确认</string>
    <string name="vi_order_state_cancel_trade">取消交易</string>
    <string name="vi_order_state_wait_timeout">等待超时</string>
    <string name="vi_order_state_reject_transaction">拒绝交易</string>
    <string name="vi_order_state_wait_pay">等待付款</string>
    <string name="vi_order_state_buyer_cancels">买家取消</string>
    <string name="vi_order_state_buyer_pay_timeout">买家超时付款</string>
    <string name="vi_order_state_paid">已经付款</string>
    <string name="vi_order_state_seller_timeout">卖家超时转币</string>
    <string name="vi_order_state_trading_closed">交易关闭</string>
    <string name="vi_order_state_transaction_successful">交易成功</string>
    <string name="vi_order_state_other"> </string>
    <string name="vi_order_info_error">订单信息出错</string>
    <string name="vi_usdt_trc20">USDT-TRC20</string>
    <string name="vi_usdt">USDT</string>
    <string name="vi_usdt_hint">请输入要兑换的USDT数量</string>
    <string name="vi">Vi</string>
    <string name="vi_get_verified_param_error">获取认证信息失败，请重试</string>
    <string name="vi_ocr_failure">识别失败(%1$s)，请重试</string>
    <string name="vi_incorrect_param">传入参数有误，请重试</string>
    <string name="vi_orc_login_failure">OCR登录失败，请重试</string>
    <string name="vi_hint">请输入要兑换的Vi数量</string>
    <string name="vi_buy_now">立即购买</string>
    <string name="vi_rate_txt">Vi钱包汇率</string>
    <string name="vi_rate_ph">≈ %1$s Vi</string>
    <string name="vi_tip">请确保到账金额不低于 100 USDT，否则无法进行处理，并且无法退款。</string>
    <string name="vi_cancel_sale">取消出售</string>
    <string name="vi_in_progress">进行中</string>
    <string name="vi_pending_order_closed">挂单已关闭</string>
    <string name="vi_sold_out">已售罄</string>
    <string name="vi_expired">已过期</string>
    <string name="vi_system_close">系统关闭</string>
    <string name="vi_locked">已锁定</string>
    <string name="vi_abnormal">异常</string>
    <string name="vi_pending_order">挂单</string>
    <string name="vi_pending_order_success">挂单成功</string>
    <string name="vi_available_quantity">可售数量</string>
    <string name="vi_cyn">CNY</string>
    <string name="vi_default_rate">1.00</string>
    <string name="vi_default_unit_price">默认单价</string>
    <string name="vi_sold_by_quantity">按数量出售</string>
    <string name="vi_sold_by_quantity_hint_ph">最低出售Vi币 %1$d</string>
    <string name="vi_sold_by_quantity_limit_ph">最高出售量为%1$d Vi</string>
    <string name="vi_unit_price_setting">单价设定</string>
    <string name="vi_min_starting_price">最小起售</string>
    <string name="vi_min_starting_price_hint_ph">最低不得少于%1$d</string>
    <string name="vi_min_starting_price_limit_ph">最小起售%1$d Vi</string>
    <string name="vi_max_starting_price_limit_ph">最大起售%1$d Vi</string>
    <string name="vi_sel_payment_method">选择收款方式</string>
    <string name="vi_pls_sel_payment_method">请选择收款方式</string>
    <string name="vi_all">全部</string>
    <string name="vi_kind_tips">温馨提示</string>
    <string name="vi_tips">提示</string>
    <string name="vi_pending_order_tip">1.【实名付款】请使用与平台实名信息一致的帐户付款\n2.【信用等级】成交单量，成交效率将影响您的信用等级， 等级高的用户交易推送靠前\n3.【交易】请实时留意订单状态\n4.【出售】请务必确认收到款项再进行打币\n5.【订单完成】确认收款后，请尽快打币完成交易，订单出现异常请第一时间暂停交易并申诉\n6.未按规定操作，平台不予承担责任</string>
    <string name="vi_confirm_pending_order">确认挂单</string>
    <string name="vi_rescan">重新扫描</string>
    <string name="vi_verified_upload_tip">请上传您的身份证件照片，确保所有信息清晰可见</string>
    <string name="vi_pls_check_consistent">请核对身份信息是否与身份证一致</string>
    <string name="vi_sure">确定</string>
    <string name="vi_confirm">确认</string>
    <string name="vi_close">关闭</string>
    <string name="vi_payment_psw_check_tip">输入交易密码再次确认\n添加新的付款方式</string>
    <string name="vi_add_payment_method">新增收款方式</string>
    <string name="vi_add_payment_method_limit_ph">最多可添加%1$d个收款方式</string>
    <string name="vi_payment_method_empty">暂无收付款方式</string>
    <string name="vi_add_collection_method">新增收付款方式</string>
    <string name="vi_add_collection_method2">添加收付款方式</string>
    <string name="vi_add_collection_method_success">添加收付款方式成功</string>
    <string name="vi_receive_payment_tip">仅支持绑定实名者本人微信，支付宝，银行卡。</string>
    <string name="vi_del_receive_payment_failure">删除失败</string>
    <string name="vi_del_receive_payment_success">删除成功</string>
    <string name="vi_del_receive_payment_msg">是否删除当前(%1$s)收付款方式?</string>
    <string name="vi_del">删除</string>
    <string name="vi_order_now">立即下单</string>
    <string name="vi_buy_order_notice_title">温馨提示</string>
    <string name="vi_buy_order_notice_title1">谨防诈骗</string>
    <string name="vi_buy_order_notice">1.【实名付款】请使用与平台实名信息一致的帐户付款\n2.【信用等级】成交单量，成交效率将影响您的信用等级， 等级高的用户交易推送靠前\n3.【交易】请实时留意订单状态\n4.【售出】请务必确认收到款项再进行打币\n5.【订单完成】确认收款后，请尽快打币完成交易，订单出现异常请第一时间暂停交易并申诉\n6.未按规定操作，平台不予承担责任</string>
    <string name="vi_actual_payment">实付款</string>
    <string name="vi_purchase_quantity">购买数量</string>
    <string name="vi_sel_receipt_method">选择收款方式</string>
    <string name="vi_trade_desc">%s 成交量 ｜ %s%% 成交率 </string>
    <string name="vi_viPrice">%s CNY\n单价</string>
    <string name="vi_viNum">%s Vi\n数量</string>
    <string name="vi_seller_receipt_method">卖家收款方式</string>
    <string name="vi_choose_pay_method">请选择付款方式</string>
    <string name="vi_buy_order_popup_notice">1.恶意下单传假图平台将予封号处理\n2.卖家同意后，15分钟内完成打款并提交支付凭证\n3.下单后无辜取消订单将扣除一定信用分\n4.交易期间保持手机畅通并留意短信提醒\n5.请实名账户打款，否则卖家有权拒绝发货\n6.请确认本次购买的Vi币单价</string>
    <string name="vi_buy_order_popup_checkbox_text">我已阅读并知晓以上内容</string>
    <string name="vi_buy_order_amount_equals">≈</string>
    <string name="vi_buy_order_amount">%s CNY</string>
    <string name="vi_buy_order_limit_amount">限额%s-%s</string>
    <string name="vi_add_payment_method_tip">账户正在保护，请放心添加</string>
    <string name="vi_pay_type">支付类型</string>
    <string name="vi_name">姓名</string>
    <string name="vi_pls_input_real_name">请输入真实姓名</string>
    <string name="vi_id_num">身份证号</string>
    <string name="vi_pls_input_id_num">请输入您的身份证号</string>
    <string name="vi_pls_enter_right_id_num">请输入正确的身份证号</string>
    <string name="vi_wallet_number">钱包编号</string>
    <string name="vi_pls_enter_right_wallet_number">请输入正确的钱包编号</string>
    <string name="vi_wallet_number_hint">请输入您实名一致的钱包编号</string>
    <string name="vi_tip_wallet_number">购买，出售需添加实名者本人名下的数字人民币钱包编号，请务必填写准确无误。</string>
    <string name="vi_tip_wechat">购买，出售需添加实名者本人名下的微信收款码，需点击保存收款码到相册进行上传绑定。</string>
    <string name="vi_tip_alipay">购买，出售需添加实名者本人名下的支付宝收钱码，需点击保存收钱码到相册进行上传绑定。</string>
    <string name="vi_tip_bank">购买，出售需添加实名者本人名下的银行卡，请务必填写准确无误。</string>
    <string name="vi_account">账号</string>
    <string name="vi_account_wechat_hint">请输入您的微信号</string>
    <string name="vi_account_alipay_hint">请输入您的支付宝账号</string>
    <string name="vi_pls_enter_right_wechat_account">请输入正确的微信号</string>
    <string name="vi_pls_enter_right_alipay_account">请输入正确的支付宝账号</string>
    <string name="vi_payment_code">收款码</string>
    <string name="vi_payment_code_blank">请上传收款码</string>
    <string name="vi_payment_code_tip">（支持JPG/JPEG/PNG/BMP格式，小于1MB）</string>
    <string name="vi_image_format_limit">请上传JPG/JPEG/PNG/BMP格式的图片</string>
    <string name="vi_image_format_limit2">请上传JPG/JPEG/PNG格式的图片</string>
    <string name="vi_video_format_limit">请上传MP4/AVI/FLV格式的视频</string>
    <string name="vi_video_file_empty">视频文件错误</string>
    <string name="vi_video_size_limit">视频最大不能超过8M</string>
    <string name="vi_verified_fail_pls_retry">认证失败，请重新认证</string>
    <string name="vi_bank_num">银行卡号</string>
    <string name="vi_pls_enter_right_bank_num">请输入正确的银行卡号</string>
    <string name="vi_bank_num_hint">请输入您的银行卡号</string>
    <string name="vi_bank_name">银行名称</string>
    <string name="vi_bank_name_hint">请输入您的银行名称</string>
    <string name="vi_bank_branch">开户支行</string>
    <string name="vi_bank_branch_hint">请输入您的开户支行</string>
    <string name="vi_add">添加</string>
    <string name="vi_verified_tips">1、实名需和身份证一致，否则无法通过校验\n2、身份认证后，只能绑定该身份所办理的银行卡</string>
    <string name="vi_verified_ph">已认证：%1$s</string>
    <string name="vi_verified_tips2">为了您的交易安全，实名后不可更改\n请绑定实名注册的账号或卡号</string>
    <string name="vi_verified_failure">实名认证失败，请重试</string>
    <string name="vi_psw_set_tips">为保障账户安全，请不要设置与其它常用软件相同的密码</string>
    <string name="vi_payment_psw">支付密码</string>
    <string name="vi_pls_enter_payment_psw">请输入支付密码</string>
    <string name="vi_payment_psw_hint">6位纯数字</string>
    <string name="vi_payment_psw_limit_ph">请输入6位纯数字%1$s</string>
    <string name="vi_confirm_payment_psw">确认密码</string>
    <string name="vi_pls_enter_confirm_payment_psw">请输入确认密码</string>
    <string name="vi_psw_inconsistent">两次密码输入不一致</string>
    <string name="vi_enter_argot">输入暗语</string>
    <string name="vi_pls_enter_argot">请输入暗语</string>
    <string name="vi_enter_argot_tips">*请谨记设置暗语，密码丢失后，可通过暗语重新设置</string>
    <string name="vi_enter_argot_hint">中文字母数字6–12位</string>
    <string name="vi_enter_argot_limit">请输入中文字母数字6–12位</string>
    <string name="vi_argot_tip_ph">暗语提示：%1$s</string>
    <string name="vi_retrieve_argot">找回暗语</string>
    <string name="vi_retrieve_psw"><u>找回密码</u></string>
    <string name="vi_argot_error">暗语错误</string>
    <string name="vi_psw_old">旧密码</string>
    <string name="vi_psw_old_error">旧密码错误</string>
    <string name="vi_pls_enter_psw_old">请输入旧密码</string>
    <string name="vi_psw_old_hint">请输入当前支付密码</string>
    <string name="vi_psw_new">新密码</string>
    <string name="vi_pls_enter_psw_new">请输入新密码</string>
    <string name="vi_psw_reset_success">支付密码重置成功</string>
    <string name="vi_psw_reset_failure">支付密码重置失败</string>
    <string name="vi_psw_set_success">支付密码设置成功</string>
    <string name="vi_psw_set_failure">支付密码设置失败</string>
    <string name="vi_psw_change_success">支付密码修改成功</string>
    <string name="vi_psw_change_failure">支付密码修改失败</string>
    <string name="vi_query_time">查询时间</string>
    <string name="vi_today">今日</string>
    <string name="vi_previous_day">上一日</string>
    <string name="vi_next_day">下一日</string>
    <string name="vi_order_detail_cancel_order">取消订单</string>
    <string name="vi_order_detail_trade_order_code">交易单号</string>
    <string name="vi_order_detail_trade_time">下单时间</string>
    <string name="vi_order_detail_pay_method">支付方式</string>
    <string name="vi_order_detail_buyer_name">买家姓名</string>
    <string name="vi_order_detail_trade_with_me">与我成交</string>
    <string name="vi_order_detail_wait_confirm">待确认</string>
    <string name="vi_order_detail_wait_pay">待支付</string>
    <string name="vi_order_detail_wait_delivered">待发货</string>
    <string name="vi_order_detail_wait_evaluated">待评价</string>
    <string name="vi_order_detail_wait_confirm_time">00:05:00</string>
    <string name="vi_order_detail_send_usually_msg_tip">发送常用语</string>
    <string name="vi_order_detail_order_state_tip">下单成功，等待卖家确定订单</string>
    <string name="vi_order_info">订单信息</string>
    <string name="vi_order_detail_trade_num">交易数量</string>
    <string name="vi_order_detail_unit_price">单价</string>
    <string name="vi_enter_buy_tip_1">由于工商银行最新风控政策，需要买卖双方确认才能使用款项，为了避免不必要的麻烦，请买家，卖家停止使用工商银行进行购买或出售。\n1；首次交易请您仔细阅读买卖教程。\n2；请保持手机网络畅通，随时留意交易短信提醒。\n3；恶意下单/伪造打款图将冻结账户资金。\n4；若出现与实名不一致打款请勿发货并提交申诉。\n5；打款后及时上传交易凭证，避免超时。\n6；坚决抵制严惩利用vi商城进行，诈骗，洗钱。</string>
    <string name="vi_enter_buy_tip_2">1；务必登录收款账户确认到账后才发货！\n2；商家码收款切勿相信顾客留言到账信息！\n3；假冒银行发送到账短信，请注意识别！\n4；切勿添加好友，私聊交易，谨防受骗！\n5；账号借予他人登录，伪造打款图将永久冻结！</string>
    <string name="vi_enter_buy_tip_3">1；为了您的资金安全，请勿泄露手机验证码和交易密码！\n2；请勿私下交易，任何以低价折扣为借口要求私下交易都是骗子！\n3；不法分子冒充官方客服打电话催促发货，请挂断电话及时联系客服！\n4；vi商城不会使用第三方软件与客户联系，若有任何问题请咨询7*24小时客服！</string>
    <string name="vi_account_avatar">头像</string>
    <string name="vi_account_nick_name">昵称</string>
    <string name="vi_account_phone_num">手机号</string>
    <string name="vi_account_version_code">版本号</string>
    <string name="vi_title_edit_nickname">编辑昵称</string>
    <string name="vi_edit_nickname_tip_left">(长度限制8个中文)</string>
    <string name="vi_edit_nickname_tip_right">每月可改名一次</string>
    <string name="vi_edit_nickname_save">保存</string>
    <string name="vi_cur_state">当前状态</string>
    <string name="vi_recharge">充值</string>
    <string name="vi_withdraw">提现</string>
    <string name="vi_expenditure">支出</string>
    <string name="vi_income">收入</string>
    <string name="vi_withdraw_income">提现收入</string>
    <string name="vi_recharge_deduction">充值扣款</string>
    <string name="vi_pending_order_deduction">挂单扣款</string>
    <string name="vi_close_pending_order_payment">关闭挂单回款</string>
    <string name="vi_transaction_successful">交易成功</string>
    <string name="vi_transaction_successful_system">交易成功(系统)</string>
    <string name="vi_close_pending_order_payment_system">关闭挂单回款(系统)</string>
    <string name="vi_platform_recharge">平台充值</string>
    <string name="vi_platform_withdraw">平台提现</string>
    <string name="vi_platform_transfer">平台转账</string>
    <string name="vi_system_recharge">系统充值</string>
    <string name="vi_system_deduction">系统扣款</string>
    <string name="vi_success">成功</string>
    <string name="vi_order_state_agree_transaction">同意交易</string>
    <string name="vi_customer_service_title_default">VIP客服</string>
    <string name="vi_customer_service_title_1">VI商城24小时在线客服</string>
    <string name="vi_customer_service_title_2">申诉在线客服</string>
    <string name="vi_customer_service_tip_1">7*24小时在线客服为你服务</string>
    <string name="vi_customer_service_tip_2">处理申诉相关事宜</string>
    <string name="vi_customer_service_title_3">高级vip客服</string>
    <string name="vi_order_state_had_paid">我已转账</string>
    <string name="vi_buy_notes">购买须知</string>
    <string name="vi_authentication">身份认证</string>
    <string name="vi_authentication_tip">为了切实保护用户权益，我们根据业务开展的实际情况，用户需【身份认证】后方可下单。</string>
    <string name="vi_order_coin">下单买币</string>
    <string name="vi_order_coin_tip">选择符合您购买数量的卖方进行下单，通过金融卡/电子钱包等支付方式转账给卖家，Vipay官方不会向您收取手续费。</string>
    <string name="vi_confirm_payment">确认收款</string>
    <string name="vi_confirm_payment_tip">转账完成后，点选（已完成付款），并上传转帐截图，卖家确认收款后，Vipay将存入您的钱包账号。</string>
    <string name="vi_confirm_payment_tip2">出售时，务必确认收到的款项再进行转币，未按规定操作，平台不承担任何责任</string>
    <string name="vi_trade_complete">交易完成</string>
    <string name="vi_verified_tip">*您还未实名认证，请先前往认证</string>
    <string name="vi_go_to_verified">去实名认证</string>
    <string name="vi_pending_order_notes">挂单须知</string>
    <string name="vi_pending_order_sell_coin">挂单卖币</string>
    <string name="vi_pending_order_sell_coin_tip">请设定售卖单价与数量，需绑定收付款方式，收付款信息必须与您的实名信息一致，VI币官方不会向您收取手续费</string>
    <string name="vi_sell_amount_ph">卖出 %1$d Vi</string>
    <string name="vi_btn_text_commit">提交</string>
    <string name="vi_upload_payment_voucher_tip">如遇到上传失败，请您退出重新提交，或者填写相关转账信息。</string>
    <string name="vi_modify_success">修改成功</string>
    <string name="vi_modify_failure">修改失败，请重试</string>
    <string name="vi_cancel_sell_confirm">是否确认取消出售？</string>
    <string name="vi_go_to_certification">前往认证</string>
    <string name="vi_pls_complete_verified_first">请先完成实名认证</string>
    <string name="vi_sell_order_notice_title">风险提示</string>
    <string name="vi_order_detail_chat_list_bottom_seller_tip1">1，同意交易后，请不要离开当前页面\n2，注意订单变化，以免造成财产损失\n3，收款账户未收到资金请勿确认转币\n4，请保持手机畅通并留意短信提醒</string>
    <string name="vi_order_detail_chat_list_bottom_seller_tip2">买家使用实名为%s的%s支付\n请确认%s支付方式，谢谢！</string>
    <string name="vi_order_detail_chat_list_bottom_seller_tip3">1，务必登录收款账户确认到账后才发货！\n2，近期骗子冒充客服较多，只要交易过程中出现客服肯定是骗子！\n3，遇到任何问题必须通过【vi商城内唯一在线客服】进行处理，其他均是骗子！</string>
    <string name="vi_order_detail_chat_list_bottom_buyer_tip">请使用实名为%s的%s支付，保证交易的安全性。\n检测到您本次支付账号为：\n%s\n请您使用以上%s账号完成支付，谢谢！</string>
    <string name="vi_new_version_found">发现新版本</string>
    <string name="vi_version_ph">版本 %1$s</string>
    <string name="vi_exit">退出VI钱包</string>
    <string name="vi_update">立即升级</string>
    <string name="vi_deliver_input_psw_amount">%dCNY</string>
    <string name="vi_deliver_input_psw_unit_price">单价%sCNY</string>
    <string name="vi_deliver_input_psw_vi_num">数量%svi</string>
    <string name="vi_deliver_input_psw_title">实收金额</string>
    <string name="vi_deliver_input_psw_tip">*请确认收款金额后发货</string>
    <string name="vi_order_detail_order_over_amount">实付款：%dCNY</string>
    <string name="vi_trade_with_me_num">｜%s 与我成交</string>
    <string name="vi_sel_pay_method">选择付款方式</string>
    <string name="vi_order_count_max">9+</string>
    <string name="vi_user_id_ph">ID: %1$s</string>
    <string name="vi_upload_payment_voucher">上传支付凭证</string>
    <string name="vi_upload_payment_voucher_tip1">收付款信息平台将加密处理，请放心通知</string>
    <string name="vi_upload_payment_voucher_tip2">请勿上传非相关凭证，否则可能导致封号</string>
    <string name="vi_payment_voucher">支付凭证</string>
    <string name="vi_transfer_card_num">转账卡号</string>
    <string name="vi_input_card_num_hint">请输入转账卡号后4位</string>
    <string name="vi_transfer_bank">转账银行</string>
    <string name="vi_transfer_bank_hint">请输入转账银行</string>
    <string name="vi_transfer_num">转账单号</string>
    <string name="vi_transfer_num_hint">请输入转账单号后4位</string>
    <string name="vi_trade_vi_num">交易数量：%sVi</string>
    <string name="vi_sel_payment_method_notice">同类支付方式只支持一个</string>
    <string name="vi_verified_info">校验身份信息</string>
    <string name="vi_verified_info_tip1">1.请使用政府核发的证件正本\n2.请将证件摆放于单色背景\n3.拍摄证件时务必确认照片清晰且无遮蔽或反光等情形\n4.请勿使用黑白照片</string>
    <string name="vi_verified_info_tip2">警告:代替他人认证可能造成严重后果影响您自身合法权益</string>
    <string name="vi_next_step">下一步</string>
    <string name="vi_one">1</string>
    <string name="vi_two">2</string>
    <string name="vi_three">3</string>
    <string name="vi_upload_id">上传身份证</string>
    <string name="vi_face_verified">人脸认证</string>
    <string name="vi_completed">完成</string>
    <string name="vi_verified_upload_video_tip1">请录制一段3–5秒的视频，视频中需包含\n张嘴、闭眼动作</string>
    <string name="vi_verified_upload_video_tip2">请录制一段3–5秒的视频，视频中需包含\n闭眼、张嘴动作</string>
    <string name="vi_facing_phone">正对手机</string>
    <string name="vi_sufficient_light">光线充足</string>
    <string name="vi_face_uncovered">脸无遮挡</string>
    <string name="vi_line">线路</string>
    <string name="vi_customer_service">Vi客服</string>
    <string name="vi_account_frozen_tip">您的账户异常，请联系vi客服</string>
    <string name="vi_sure_agree_trade">确认同意交易？</string>
    <string name="vi_sure_reject_trade">是否拒绝交易？</string>
    <string name="vi_login_again">重新登录</string>
    <string name="vi_verify_waiting">正在审核中，大约1–3分钟，请耐心等待……</string>
</resources>