package com.trade.vi.ui.fragment

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.WindowManager
import androidx.core.content.ContextCompat
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.divider
import com.drake.brv.utils.linear
import com.drake.brv.utils.setup
import com.hjq.bar.OnTitleBarListener
import com.hjq.bar.TitleBar
import com.trade.vi.R
import com.trade.vi.base.BaseViDialogFragment
import com.trade.vi.bean.ViAmountRangeSelectInfo
import com.trade.vi.databinding.DialogAmountRangeChooseBinding
import com.trade.vi.databinding.DialogAmountRangeItemBinding
import com.yunbao.common.EventCode
import com.yunbao.common.bean.EventBean
import com.yunbao.common.utils.postEvent


/**
 * 选择金额区间-弹窗
 */
class DialogAmountRangeChooseFragment(var position: Int) : BaseViDialogFragment() {

    private var amountRanges = mutableListOf<ViAmountRangeSelectInfo>()
    private var selectedPosition = position
    override val bind by getBind<DialogAmountRangeChooseBinding>()

    override fun initView() {
        super.initView()
        val window = dialog?.window
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.setWindowAnimations(R.style.bottomToTopAnim)
        val params = window?.attributes
        params?.width = WindowManager.LayoutParams.MATCH_PARENT
        params?.height = WindowManager.LayoutParams.WRAP_CONTENT
        params?.gravity = Gravity.BOTTOM
        params?.dimAmount = 0.5f
        window?.attributes = params

        bind.titleBar.setOnTitleBarListener(object : OnTitleBarListener {

            override fun onRightClick(titleBar: TitleBar?) {
                super.onRightClick(titleBar)
                dismiss()
            }
        })

        bind.rv.divider {
            setDivider(resources.getDimensionPixelSize(R.dimen.dp_1), false)
            setColor(ContextCompat.getColor(requireContext(), R.color.c_vi_f3))
            includeVisible = true
        }.linear().setup {
            addType<ViAmountRangeSelectInfo>(R.layout.dialog_amount_range_item)
            onBind {
                val item = getModel<ViAmountRangeSelectInfo>()
                getBinding<DialogAmountRangeItemBinding>().apply {
                    amountRange.text = item.amountRange ?: ""
                    checkbox.isChecked = item.isChecked
                }
            }

            onFastClick(R.id.rootView) {
                var checked = getModel<ViAmountRangeSelectInfo>().isChecked
                if (checked) return@onFastClick
                if (it == R.id.rootView) checked = !checked
                setChecked(layoutPosition, checked)
            }

            onChecked { position, isChecked, isAllChecked ->
                if (selectedPosition != -1) {
                    val model = getModel<ViAmountRangeSelectInfo>(selectedPosition)
                    model.isChecked = false
                }
                val model = getModel<ViAmountRangeSelectInfo>(position)
                model.isChecked = isChecked
                notifyDataSetChanged()
                <EMAIL> = position
                postEvent(EventBean(EventCode.EVENT_AMOUNT_RANGE_SELECT, position))
                dismiss()
            }
        }.models = getAmountRanges()

        //设置为单选模式
        bind.rv.bindingAdapter.singleMode = true
    }

    private fun getAmountRanges(): MutableList<ViAmountRangeSelectInfo> {
        for ((index, name) in resources.getStringArray(R.array.amount_range).toList().withIndex()) {
            if (selectedPosition == index) {
                amountRanges.add(ViAmountRangeSelectInfo(name, true))
            } else {
                amountRanges.add(ViAmountRangeSelectInfo(name, false))
            }
        }
        return amountRanges
    }
}