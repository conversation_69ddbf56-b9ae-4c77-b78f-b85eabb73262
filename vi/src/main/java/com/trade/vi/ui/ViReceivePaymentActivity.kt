package com.trade.vi.ui

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.drake.brv.utils.setup
import com.hjq.bar.OnTitleBarListener
import com.hjq.bar.TitleBar
import com.hjq.http.EasyHttp
import com.hjq.http.listener.HttpCallbackProxy
import com.kongzue.dialogx.dialogs.CustomDialog
import com.kongzue.dialogx.interfaces.OnBindView
import com.trade.vi.R
import com.trade.vi.base.BaseViActivity
import com.trade.vi.bean.ViPaymentInfo
import com.trade.vi.databinding.ActivityViReceivePaymentBinding
import com.trade.vi.databinding.ItemCollectionMethodAddBinding
import com.trade.vi.databinding.ItemReceivePaymentBinding
import com.trade.vi.utils.ViConstants
import com.trade.vi.utils.ViHttpConfig
import com.trade.vi.utils.ViLocalDataUtil
import com.trade.vi.utils.getSR
import com.yunbao.common.EventCode
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.bean.EventBean
import com.yunbao.common.popup.showUtils.CommonPopupShowUtils
import com.yunbao.common.utils.click
import com.yunbao.common.utils.fromJsonList
import com.yunbao.common.utils.getMap
import com.yunbao.common.utils.loadLocalImg
import com.yunbao.common.utils.nav2Act
import com.yunbao.common.utils.registerEvent
import com.yunbao.common.utils.showCenter
import com.yunbao.common.utils.showCustomToastFailed
import com.yunbao.common.utils.showCustomToastSuccess
import com.yunbao.common.utils.toJson
import com.yunbao.common.utils.toRequestBody
import com.yunbao.common.utils.unregisterEvent
import okhttp3.Call
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * 收付款
 */
@Route(path = RoutePath.PATH_VI_RECEIVE_PAYMENT)
class ViReceivePaymentActivity : BaseViActivity() {
    override val bind by getBind<ActivityViReceivePaymentBinding>()

    override fun initView() {
        registerEvent()
        bind.page.onRefresh {
            loadData()
        }
        bind.rv.setup {
            addType<ViPaymentInfo>(R.layout.item_receive_payment)
            onBind {
                val item = getModel<ViPaymentInfo>()
                getBinding<ItemReceivePaymentBinding>().apply {
                    tvLine.shapeDrawableBuilder.setSolidColor(
                        ContextCompat.getColor(
                            this@ViReceivePaymentActivity,
                            item.paymentColorResId
                        )
                    ).intoBackground()
                    tvName.text = item.tradeMethodShortName ?: ""
                    tvAccount.text = item.maskPayAccount
                }
            }
            R.id.ivDel.onClick {
                val item = getModel<ViPaymentInfo>()
                CommonPopupShowUtils.showAmountNotEnoughPopup(
                    context = this@ViReceivePaymentActivity,
                    title = getString(R.string.vi_kind_tips),
                    center = getString(
                        R.string.vi_del_receive_payment_msg,
                        item.tradeMethodShortName
                    ),
                    sureText = getString(R.string.vi_del),
                    cancelText = getString(R.string.vi_cancel),
                    clickSureListener = {
                        del(item.id)
                    },
                    clickCancelListener = null,
                    isDismissOnTouchOutsize = true
                )
            }
        }
        bind.page.stateLayout?.onEmpty {
            findViewById<TextView>(R.id.msg).setText(R.string.vi_payment_method_empty)
        }
    }

    override fun initData() {
        bind.page.autoRefresh()
    }

    private fun loadData() {
        val map = getMap(1)
        map["token"] = LiveApp.viToken
        EasyHttp.post(this)
            .body(map.toRequestBody())
            .api(ViHttpConfig.URL_GET_PAYMENT_LIST)
            .request(object : HttpCallbackProxy<Any>(this) {
                override fun onHttpSuccess(result: Any?) {
                    try {
                        if (result != null) {
                            val json = JSONObject(result.toJson())
                            val data = json.getString("data")
                            val list = fromJsonList<ViPaymentInfo>(data)
                            bind.page.addData(list)
                            ViLocalDataUtil.saveViPaymentList(list)
                        } else {
                            bind.page.showEmpty()
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        bind.page.showEmpty()
                    }
                }

                override fun onHttpStart(call: Call?) {}

                override fun onHttpEnd(call: Call?) {
                    bind.page.finishRefresh()
                }
            })
    }

    /**
     * 删除收付款
     */
    private fun del(id: String?) {
        if (id.isNullOrBlank()) {
            showCustomToastFailed(getString(R.string.vi_del_receive_payment_failure))
        } else {
            val map = getMap(2)
            map["id"] = id
            map["token"] = LiveApp.viToken
            EasyHttp.post(this)
                .body(map.toRequestBody())
                .api(ViHttpConfig.URL_DEL_RECEIVE_PAYMENT)
                .request(object : HttpCallbackProxy<Any>(this) {
                    override fun onHttpSuccess(result: Any?) {
                        try {
                            if (result != null) {
                                val json = JSONObject(result.toJson())
                                val rst = getSR(json)
                                if (rst?.isSuccess == true) {
                                    showCustomToastSuccess(getString(R.string.vi_del_receive_payment_success))
                                    bind.page.autoRefresh()
                                } else {
                                    showCustomToastFailed(rst?.msg ?: "")
                                }
                            } else {
                                showCustomToastFailed(getString(R.string.vi_del_receive_payment_failure))
                                bind.page.autoRefresh()
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            bind.page.autoRefresh()
                        }
                    }
                })
        }
    }

    override fun clickEvent() {
        bind.tvAdd.click {
            CustomDialog.show(object :
                OnBindView<CustomDialog>(R.layout.dialog_collection_method_add) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.apply {
                        findViewById<TitleBar>(R.id.titleBar).setOnTitleBarListener(object :
                            OnTitleBarListener {
                            override fun onRightClick(titleBar: TitleBar?) {
                                dialog?.dismiss()
                            }
                        })
                        findViewById<RecyclerView>(R.id.rv).setup {
                            addType<ViPaymentInfo>(R.layout.item_collection_method_add)
                            onBind {
                                val item = getModel<ViPaymentInfo>()
                                getBinding<ItemCollectionMethodAddBinding>().apply {
                                    ivIcon.loadLocalImg(item.paymentResId)
                                    tvName.text = item.tradeMethodShortName ?: ""
                                }
                            }

                            R.id.clRoot.onClick {
                                /* if (!ViLocalDataUtil.isVerified()) {
                                     CommonPopupShowUtils.showAmountNotEnoughPopup(
                                         context = this@ViReceivePaymentActivity,
                                         title = getString(R.string.vi_kind_tips),
                                         center = getString(R.string.vi_pls_complete_verified_first),
                                         cancelText = getString(R.string.vi_cancel),
                                         sureText = getString(R.string.vi_go_to_certification),
                                         clickSureListener = {
                                             nav2Act(RoutePath.PATH_VI_VERIFIED_UPLOAD_ID)
                                         },
                                         clickCancelListener = {
                                             dialog?.dismiss()
                                         })
                                     return@onClick
                                 }*/
                                val item = getModel<ViPaymentInfo>()
                                nav2Act(RoutePath.PATH_VI_ADD_PAYMENT_METHOD, Bundle().apply {
                                    putString(ViConstants.PAY_TYPE, item.payType)
                                    putString(ViConstants.NAME, item.tradeMethodShortName)
                                })
                                dialog?.dismiss()
                            }

                        }.models = ViLocalDataUtil.getPaymentMethodList()
                    }
                }
            }).showCenter(this@ViReceivePaymentActivity, CustomDialog.ALIGN.BOTTOM)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun event(event: EventBean) {
        if (event.eventCode == EventCode.EVENT_VI_ADD_PAYMENT_METHOD_SUCCESS) {
            bind.page.index = 1
            loadData()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterEvent()
    }
}