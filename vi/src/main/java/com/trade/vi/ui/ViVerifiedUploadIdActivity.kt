package com.trade.vi.ui

import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.aliyun.identity.platform.api.IdentityParams
import com.aliyun.identity.platform.api.IdentityPlatform
import com.aliyun.identity.platform.api.IdentityResponseCode
import com.hjq.http.EasyHttp
import com.hjq.http.listener.HttpCallbackProxy
import com.trade.vi.R
import com.trade.vi.base.BaseViActivity
import com.trade.vi.databinding.ActivityViVerifiedUploadIdBinding
import com.trade.vi.utils.ViHttpConfig
import com.trade.vi.utils.getSR
import com.trade.vi.utils.showFailDialog
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.utils.getMap
import com.yunbao.common.utils.nav2Act
import com.yunbao.common.utils.toJson
import com.yunbao.common.utils.toRequestBody
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject


/**
 * 实名认证--上传身份证
 */
@Route(path = RoutePath.PATH_VI_VERIFIED_UPLOAD_ID)
class ViVerifiedUploadIdActivity : BaseViActivity() {

    override val bind by getBind<ActivityViVerifiedUploadIdBinding>()

    override fun initView() {
        IdentityPlatform.getInstance().install(this)
        val metaInfo = IdentityPlatform.getMetaInfo(this)
        val map = getMap(6)
        map["token"] = LiveApp.viToken
        map["meta_info"] = metaInfo
        EasyHttp.post(this)
            .body(map.toRequestBody())
            .api(ViHttpConfig.URL_VERIFIED_CONFIG)
            .request(object : HttpCallbackProxy<Any>(this) {
                override fun onHttpSuccess(result: Any?) {
                    if (result != null) {
                        kotlin.runCatching {
                            val json = JSONObject(result.toJson())
                            val rst = getSR(json)
                            if (rst?.isSuccess == true) {
                                val certifyId = json.getString("certify_id")
                                val orderNo = json.getString("order_no")
                                if (certifyId.isNotBlank()) {
                                    startVerify(certifyId, orderNo)
                                } else {
                                    showFail()
                                }
                            } else {
                                showFail(rst?.msg ?: "")
                            }
                        }.onFailure {
                            showFail()
                        }
                    }
                }

                override fun onHttpFail(throwable: Throwable?) {
                    super.onHttpFail(throwable)
                    showFail()
                }
            })
    }

    private fun startVerify(certifyId: String, orderNo: String) {
        // 设置SDK语言
        val extParams: MutableMap<String, String> = HashMap()
        extParams[IdentityParams.SdkLanguage] = "zh-CN"
        // 开始认证
        IdentityPlatform.getInstance().verify(certifyId, extParams) { response ->
            if (response.code in arrayOf(IdentityResponseCode.IDENTITY_FAIL, IdentityResponseCode.IDENTITY_SUCCESS)) {
                //认证成功
                nav2Act(RoutePath.PATH_VI_VERIFIED_WAITING)
                finish()
            } else {
                //认证失败
                showFail("${response.code}: ${getString(R.string.vi_verified_fail_pls_retry)}")
            }
            true
        }
    }

    override fun clickEvent() {
    }

    private fun showFail(msg: String = "") {
        lifecycleScope.launch {
            delay(300)
            <EMAIL>(
                msg.ifBlank { getString(R.string.vi_verified_fail_pls_retry) },
                {
                    finish()
                })
        }
    }
}