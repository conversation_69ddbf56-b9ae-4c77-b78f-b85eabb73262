package com.trade.vi.ui.fragment

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.drake.brv.utils.divider
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.interval.Interval
import com.gyf.immersionbar.ktx.immersionBar
import com.hjq.bar.OnTitleBarListener
import com.hjq.bar.TitleBar
import com.hjq.http.EasyHttp
import com.hjq.http.listener.HttpCallbackProxy
import com.kongzue.dialogx.dialogs.CustomDialog
import com.kongzue.dialogx.interfaces.OnBindView
import com.trade.vi.R
import com.trade.vi.base.BaseViFragment
import com.trade.vi.bean.PaymentRes
import com.trade.vi.bean.ViBuyAndSellInfo
import com.trade.vi.bean.ViPaymentInfo
import com.trade.vi.databinding.FragmentTradingBuyBinding
import com.trade.vi.databinding.ItemTradingBuyBinding
import com.trade.vi.databinding.ItemTradingBuyPaymentBinding
import com.trade.vi.utils.ViHttpConfig
import com.trade.vi.utils.ViLocalDataUtil
import com.trade.vi.utils.showVerifiedDialog
import com.yunbao.common.EventCode
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.bean.EventBean
import com.yunbao.common.utils.click
import com.yunbao.common.utils.fromJsonList
import com.yunbao.common.utils.getMap
import com.yunbao.common.utils.loadImgAvatar
import com.yunbao.common.utils.loadLocalImg
import com.yunbao.common.utils.log
import com.yunbao.common.utils.nav2Act
import com.yunbao.common.utils.registerEvent
import com.yunbao.common.utils.showCustomToastFailed
import com.yunbao.common.utils.showDialogFragment
import com.yunbao.common.utils.toJson
import com.yunbao.common.utils.toRequestBody
import com.yunbao.common.utils.unregisterEvent
import okhttp3.Call
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import java.util.concurrent.TimeUnit

/**
 * 买入
 */
class TradingBuyFragment : BaseViFragment() {

    //支付方式
    private var payMethods = mutableListOf<String>()

    //当前选择的支付方式(默认没选择)
    private var selectedPayType = ""
    private var selectedPayTypeIndex = -1

    //金额区间
    private var amountRanges = mutableListOf<String>()

    //当前选择的金额区间(默认全部)
    private var selectedAmountRange = 0

    //筛选-> 数量（倒序，顺序）
    private var asceOrDsce = 0

    //轮询获取列表数据
    private var interval: Interval? = null

    override val bind by getBind<FragmentTradingBuyBinding>()

    override fun initView() {
        registerEvent()
        bind.titleBar.setOnTitleBarListener(object : OnTitleBarListener {
            override fun onLeftClick(titleBar: TitleBar?) {
                activity?.finish()
            }

            override fun onRightClick(titleBar: TitleBar?) {
                activity?.finish()
            }
        })
        bind.page.setOnRefreshListener {
            loadData(false)
        }
        bind.rvContent.divider {
            setDivider(resources.getDimensionPixelSize(R.dimen.dp_2), false)
            setColor(ContextCompat.getColor(requireContext(), R.color.c_vi_f3))
            includeVisible = false
        }.setup {
            addType<ViBuyAndSellInfo>(R.layout.item_trading_buy)
            onCreate {
                getBinding<ItemTradingBuyBinding>().rvPayment.setup {
                    addType<PaymentRes>(R.layout.item_trading_buy_payment)
                    onBind {
                        val paymentRes = getModel<PaymentRes>()
                        getBinding<ItemTradingBuyPaymentBinding>().ivIcon.loadLocalImg(paymentRes.id)
                    }
                }
            }
            onBind {
                val item = getModel<ViBuyAndSellInfo>()
                getBinding<ItemTradingBuyBinding>().apply {
                    ivAvatar.loadImgAvatar(item.avatarUrl)
                    tvName.text = item.userName ?: ""
                    //信用相关
                    val creditRatingResList = item.creditRatingResList
                    if (creditRatingResList.size == 4) {
                        ivCredit.loadLocalImg(creditRatingResList[0])
                        tvCredit.setText(creditRatingResList[1])
                        tvCredit.setTextColor(
                            ContextCompat.getColor(
                                context,
                                creditRatingResList[2]
                            )
                        )
                        tvCredit.shapeDrawableBuilder.setStrokeColor(
                            ContextCompat.getColor(
                                requireContext(),
                                creditRatingResList[2]
                            )
                        ).setStrokeSize(resources.getDimensionPixelSize(R.dimen.dp_1))
                            .setRadius(resources.getDimensionPixelSize(R.dimen.dp_2).toFloat())
                            .setSolidColor(
                                ContextCompat.getColor(
                                    requireContext(),
                                    creditRatingResList[3]
                                )
                            )
                            .intoBackground()
                    }
                    tvOrderQuantity.text =
                        getString(R.string.vi_order_quantity_ph, item.orderQuantity)
                    tvQuantity.text = getString(R.string.vi_quantity_ph, item.quantity ?: "")
                    tvLimits.text = getString(R.string.vi_limits_ph, item.limits)
                    tvPrice.text = item.priceStr
                    rvPayment.models = item.paymentResList
                }
            }
            onClick(R.id.clRoot) {
//                if (!ViLocalDataUtil.isVerified()) {
//                    //未实名，提示实名认证
//                    context.showVerifiedDialog(cancelable = true)
//                    return@onClick
//                }
                requireContext().showVerifiedDialog(cancelable = true, dismissListener = {
                    val item = getModel<ViBuyAndSellInfo>()
                    nav2Act(RoutePath.PATH_VI_PLACE_ORDER, Bundle().apply {
                        putSerializable("ViBuyAndSellInfo", item)
                    })
                })
            }
        }
        interval = Interval(30, TimeUnit.SECONDS, 30)
            .life(this)
            .onlyResumed(this)
            .subscribe {
                loadData()
            }
    }

    override fun initData() {
        super.initData()
        payMethods = resources.getStringArray(R.array.pay_method).toMutableList()
        amountRanges = resources.getStringArray(R.array.amount_range).toMutableList()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun event(event: EventBean) {
        when (event.eventCode) {
            //支付方式
            EventCode.EVENT_PAY_METHOD_SELECT -> {
                val viPaymentInfo = event.value as ViPaymentInfo
                if (viPaymentInfo.tag == this::class.simpleName) {
                    selectedPayTypeIndex = viPaymentInfo.payMethodIndex
                    selectedPayType = viPaymentInfo.payType ?: ""
                    bind.tvPaymentMethod.text = "    "
                    bind.ivPayMethodIcon.setImageResource(viPaymentInfo.paymentResId)
                    loadData()
                }
            }
            //金额区间
            EventCode.EVENT_AMOUNT_RANGE_SELECT -> {
                selectedAmountRange = event.value as Int
                bind.tvInterval.text = amountRanges[selectedAmountRange]
                loadData()
            }
            //登录成功
            EventCode.EVENT_VI_LOGIN_SUCCESS -> {
                loadData(false)
                interval?.start()
            }
        }
    }

    /**
     * 数据加载
     */
    private fun loadData(showLoading: Boolean = true) {
        val map = getMap(4)
        map["sort_by"] = if (asceOrDsce == 0) "" else "quantity"
        map["asce_or_dsce"] = asceOrDsce
        map["payment_type"] = selectedPayType
        map["find_type"] = 0
        map["trading_quantity"] = selectedAmountRange
        map["find_state"] = arrayOf(0, 1)
        map["token"] = LiveApp.viToken
        EasyHttp.post(this)
            .tag("tradingBuy")
            .body(map.toRequestBody())
            .delay(500)
            .api(ViHttpConfig.URL_BUY_LIST)
            .request(object : HttpCallbackProxy<Any>(this) {
                override fun onHttpSuccess(result: Any?) {
                    if (result != null) {
                        try {
                            val json = JSONObject(result.toJson())
                            val data = json.getString("orders")
                            log(">>>>>>>>>.data = $data")
                            if (data.isNotBlank()) {
                                val orderList = fromJsonList<ViBuyAndSellInfo>(data)
                                bind.rvContent.models = orderList
                            } else {
//                                showCustomToastFailed(getString(R.string.vi_load_error))
                            }
                        } catch (e: Exception) {
//                            showCustomToastFailed(getString(R.string.vi_load_error))
                        }
                    } else {
//                        showCustomToastFailed(getString(R.string.vi_load_error))
                    }
                }

                override fun onHttpStart(call: Call?) {
                    if (showLoading) {
                        super.onHttpStart(call)
                    }
                }

                override fun onHttpEnd(call: Call?) {
                    super.onHttpEnd(call)
                    bind.page.finishRefresh()
                }
            })

        //获取USDT汇率
//        val map2 = getMap(1)
//        map2["token"] = LiveApp.viToken
//        EasyHttp.post(this)
//            .body(map.toRequestBody())
//            .api(ViHttpConfig.URL_GET_USDT_RATE)
//            .request(object : HttpCallbackProxy<Any>(this) {
//                override fun onHttpSuccess(result: Any?) {
//                    if (result != null) {
//                        try {
//                            val json = JSONObject(result.toJson())
//                            val rate = (json.getString("usdt_rate").toFloatOrNull() ?: 0f).format2()
//                            bind.tvRate.text = getString(R.string.usdt_exchange_vi_rate_ph, rate)
//                        } catch (e: Exception) {
//                            bind.tvRate.text = getString(R.string.usdt_exchange_vi_rate_ph, "0.00")
//                        }
//                    } else {
//                        bind.tvRate.text = getString(R.string.usdt_exchange_vi_rate_ph, "0.00")
//                    }
//                }
//
//                override fun onHttpStart(call: Call?) {
//                }
//
//                override fun onHttpEnd(call: Call?) {
//                }
//            })
    }

    override fun initImmersionBar() {
        super.initImmersionBar()
        immersionBar {
            titleBar(bind.titleBar)
        }
    }

    override fun clickEvent() {
        bind.apply {
            clExchange.click {
                nav2Act(RoutePath.PATH_VI_USDT_EXCHANGE_VI)
            }
            tvPaymentMethod.click {
                val payChannelSelectFragment =
                    DialogPayMethodChooseFragment(selectedPayTypeIndex, getAllPayMethods())
                showDialogFragment(payChannelSelectFragment)
            }

            tvInterval.click {
                val amountRangeSelectFragment = DialogAmountRangeChooseFragment(selectedAmountRange)
                showDialogFragment(amountRangeSelectFragment)
            }

            tvAmount.click {
                when (tvAmount.tag) {
                    R.drawable.icon_vi_sorting_asc -> {
                        setTextViewDrawableRight(
                            tvAmount,
                            R.drawable.icon_vi_sorting_desc
                        )
                        asceOrDsce = -1
                        loadData()
                    }

                    R.drawable.icon_vi_sorting_desc -> {
                        setTextViewDrawableRight(
                            tvAmount,
                            R.drawable.icon_vi_sorting_asc
                        )
                        asceOrDsce = 1
                        loadData()
                    }

                    else -> {
                        setTextViewDrawableRight(
                            tvAmount,
                            R.drawable.icon_vi_sorting_asc
                        )
                        asceOrDsce = 1
                        loadData()
                    }
                }
            }

            tvMenu.click {
                CustomDialog.show(object : OnBindView<CustomDialog>(R.layout.dialog_buy_page_menu) {
                    override fun onBind(dialog: CustomDialog?, v: View?) {
                        v?.apply {
                            findViewById<ConstraintLayout>(R.id.clPaymentManager)?.click {
                                nav2Act(RoutePath.PATH_VI_RECEIVE_PAYMENT)
                                dialog?.dismiss()
                            }

                            findViewById<ConstraintLayout>(R.id.clPayPsw)?.click {
                                val info = ViLocalDataUtil.getViAccountInfo()
                                if (info == null) {
                                    showCustomToastFailed(getString(R.string.vi_load_error))
                                } else {
                                    if (info.psw.isNullOrBlank()) {
                                        nav2Act(RoutePath.PATH_VI_PSW_SET)
                                    } else {
                                        nav2Act(RoutePath.PATH_VI_PSW_CHANGE)
                                    }
                                }
                                dialog?.dismiss()
                            }

                            findViewById<TitleBar>(R.id.titleBar).setOnTitleBarListener(object :
                                OnTitleBarListener {

                                override fun onRightClick(titleBar: TitleBar?) {
                                    super.onRightClick(titleBar)
                                    dialog?.dismiss()
                                }
                            })
                        }
                    }
                }).setAlign(CustomDialog.ALIGN.BOTTOM)
                    .setMaskColor(
                        ContextCompat.getColor(
                            requireContext(),
                            com.yunbao.common.R.color.skin_color_dialog_mask
                        )
                    )
            }
        }
    }

    private fun setTextViewDrawableRight(textView: TextView, resDrawableId: Int) {
        textView.tag = resDrawableId
        val drawableRight =
            context?.let { it1 -> ContextCompat.getDrawable(it1, resDrawableId) }
        drawableRight?.setBounds(
            0,
            0,
            resources.getDimensionPixelSize(R.dimen.dp_8),
            resources.getDimensionPixelSize(R.dimen.dp_15)
        )
        textView.setCompoundDrawables(null, null, drawableRight, null)
    }

    private fun getAllPayMethods(): MutableList<Any> {

        val payMethods = mutableListOf<Any>()
        for ((index, payMethodName) in resources.getStringArray(R.array.pay_method).toList()
            .withIndex()) {

            val viPaymentInfo = ViPaymentInfo()
            viPaymentInfo.payMethodIndex = index
            viPaymentInfo.payAccount = payMethodName
            viPaymentInfo.tag = TradingBuyFragment::class.simpleName!!
            when (payMethodName) {
                "银行卡" -> viPaymentInfo.payType = "BANK"
                "微信" -> viPaymentInfo.payType = "WECHAT"
                "支付宝" -> viPaymentInfo.payType = "ALIPAY"
                "数字人民币" -> viPaymentInfo.payType = "ECNY"
            }
            viPaymentInfo.hasSel = index == selectedPayTypeIndex
            payMethods.add(viPaymentInfo)
        }

        return payMethods
    }

    override fun onDestroyView() {
        super.onDestroyView()
        interval?.cancel()
        interval = null
        unregisterEvent()
    }

    companion object {
        @JvmStatic
        fun newInstance() = TradingBuyFragment()
    }
}