package com.trade.vi.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.viewbinding.ViewBinding
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ktx.immersionBar
import com.hjq.http.listener.OnHttpListener
import com.trade.vi.widget.LoadingDialog
import com.yunbao.common.utils.showCustomToastFailed
import com.yunbao.common.utils.showCustomToastSuccess
import github.leavesczy.reactivehttp.viewmodel.IUIActionEventObserver
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.Call


abstract class BaseViFragment: Fragment(), IUIActionEventObserver, OnHttpListener<Any> {
    abstract val bind: ViewBinding?

    //是否第一次加载
    private var isFirst: Boolean = true

    protected inline fun <reified T> getBind(): Lazy<T> where T : ViewBinding {
        return lazy {
            val clazz = T::class.java
            val method = clazz.getMethod("inflate", LayoutInflater::class.java)
            method.invoke(null, layoutInflater) as T
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initParams()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        bind?.root?.apply {
            return this
        }
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        if (isEnableImmersionBar()) {
            initImmersionBar()
        }
        clickEvent()
        initViewObservable()
        initData()
    }

    private var myLoadingDialog: LoadingDialog? = null

    override fun showLoading() {
        dismissLoading()
        try {
            myLoadingDialog = LoadingDialog(requireContext())
            myLoadingDialog?.showLoadingNotCancelable()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun showToast(msg: String) {
        activity?.let {
            if (msg.isNotBlank()) {
                showCustomToastSuccess(msg)
            }
        }
    }

    override fun finishView() {
        activity?.finish()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dismissLoading()
    }

    override fun dismissLoading() {
        myLoadingDialog?.takeIf { it.isLoading() }?.dismiss()
        myLoadingDialog = null
    }

    open fun isEnableImmersionBar() = true

    open fun initImmersionBar() {
        try {
            immersionBar {
                hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
                transparentStatusBar()
                statusBarDarkFont(true)
            }
        } catch (e: Exception) {}
    }

    open fun clickEvent() {}
    open fun initParams() {}

    open fun initViewObservable() {}

    //两个方法选择一个加载就可
    //第一次进来时候的懒加载
    open fun lazyLoadData() {}

    //每次切换到这个界面都要刷新的数据
    open fun reloadData() {}

    override fun onResume() {
        super.onResume()
        onVisible()
    }

    private fun onVisible() {
        if (lifecycle.currentState == Lifecycle.State.STARTED && isFirst) {
            lifecycleScope.launch {
                delay(lazyLoadTime())
                lazyLoadData()
                reloadData()
                isFirst = false
            }
        } else {
            reloadData()
        }
    }

    /**
     * 延迟加载 防止 切换动画还没执行完毕时数据就已经加载好了，这时页面会有渲染卡顿  bug
     * 这里传入你想要延迟的时间，延迟时间可以设置比转场动画时间长一点 单位： 毫秒
     * 不传默认 300毫秒
     * @return Long
     */
    open fun lazyLoadTime(): Long {
        return 300
    }

    open fun initView() {}

    open fun initData() {}

    open fun isShouldHideInputLL(v: View?, event: MotionEvent): Boolean {
        if (v != null) {
            val l = intArrayOf(0, 0)
            v.getLocationInWindow(l)
            val left = l[0]
            val top = l[1]
            val bottom = top + v.height
            val right = left + v.width
            return !(event.x > left && event.x < right && event.y > top && event.y < bottom)
        }
        // 如果焦点不是EditText则忽略，这个发生在视图刚绘制完，第一个焦点不在EditView上
        return false
    }

    override val lifecycleSupportedScope: CoroutineScope
        get() = viewLifecycleOwner.lifecycleScope

    override fun onHttpStart(call: Call?) {
        showLoading()
    }

    override fun onHttpSuccess(result: Any?) {
    }

    override fun onHttpFail(throwable: Throwable?) {
        throwable?.message?.let {
            showCustomToastFailed(it)
        }
    }

    override fun onHttpEnd(call: Call?) {
        dismissLoading()
    }
}