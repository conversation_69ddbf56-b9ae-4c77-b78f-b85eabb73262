plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.trade.vi'
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            // Note: shrinkResources not available for library modules
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation fileTree(include: ['*.aar'], dir: 'libs')
    implementation 'androidx.core:core-ktx:1.8.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.8.0'
//    implementation 'androidx.activity:activity-ktx:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    //经典刷新头
    implementation  'io.github.scwang90:refresh-header-classics:2.1.0'
    //倒计时
    implementation 'com.github.liangjingkanji:Interval:1.0.3'
    //
    implementation project(':common')
    //arouter
    //阿里 ARouter
    implementation rootProject.ext.dependencies["arouter"]
    kapt rootProject.ext.dependencies["arouter-compiler"]
    //网络请求
//    api 'com.github.getActivity:EasyHttp:12.8'
    // Gson
//    api 'com.github.getActivity:GsonFactory:9.6'
    //标题
    api 'com.github.getActivity:TitleBar:10.5'
    //密码框
    implementation 'io.github.chaosleung:pinview:1.4.4'
    // 吐司框架
    implementation 'com.github.getActivity:Toaster:12.6'
    //
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
    //人脸识别--start
    implementation 'com.squareup.okio:okio:2.2.2'
    implementation 'com.aliyun.dpa:oss-android-sdk:2.9.4'
    implementation 'com.alibaba:fastjson:1.1.72.android'
    //人脸识别--end
}