plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'org.jetbrains.kotlin.android'
    id 'com.alibaba.arouter'
    //观测云
    id 'ft-plugin'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion '33.0.0'

    signingConfigs {
        release {
            v1SigningEnabled true
            v2SigningEnabled true
            keyAlias 'liveapp'
            keyPassword 'FDF12B7C03E18209'
            storeFile file('../live.jks')
            storePassword 'D16DFAC4FBBC6015'
        }

        debug {
            keyAlias 'izpan'
            keyPassword '123456'
            storeFile file('../yunbao_dev.jks')
            storePassword '123456'
        }
    }


    aaptOptions {
        // Enable cruncher for better PNG optimization
        cruncherEnabled = true
        useNewCruncher = true
        noCompress "wav,mp3"  //表示不让aapt压缩的文件后缀
    }

    defaultConfig {
        minSdk rootProject.ext.android.minSdkVersion
        targetSdk rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        multiDexEnabled true
        ndk {
            // Optimize for size: only support modern 64-bit architecture
            // Remove armeabi-v7a to reduce APK size significantly
            abiFilters "arm64-v8a"
        }
        kapt {
//            generateStubs = true
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }

    }
    buildTypes {
        release {
            debuggable false
            //----以下两项游戏盾需要设置成 false
            minifyEnabled true
            shrinkResources true
            //-----
            signingConfig signingConfigs.release
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "AES_KEY_DOMAIN", "\"mfp35NenyXKx7CNp\""
            buildConfigField "String", "AES_VI_DOMAIN", "\"DZJXTTFr100Uycss\""
            buildConfigField "String", "KEY_BYTE_DUN_1", "\"fb7fd4ac673efcfc3be64eb541b357e5\""
            buildConfigField "String", "UUID_BYTE_DUN_1", "\"FMLpBXr1nJn8QFxj1IdZ9YAEvp38ZCKLBVd7VMGw1dKm3YgkeltWQp1tYjy/90Wui+hXj5w61hs1XLQngq2RQ8fpRqdxt5UJcBNnPZSeWdS/FszeXGb1LoT3pWJlnv6XgFiYLj6X0LZ4L1lth34w0u5uJ+I7asBIdKYIwXlgO2uwt5rJ0dqCF38KRKJGEpHvPVBAlpkgsnCO3yMyTDJ/6ZLW1e5T5kCzSV8=\""
            buildConfigField "String", "KEY_BYTE_DUN_2", "\"cfc4c4fedecd92f031ffed5a1095da38\""
            buildConfigField "String", "UUID_BYTE_DUN_2", "\"1qSJYhWhRdeuKKgzYH3s/nRd4sBai+fpHXR1sb7wMvMedJ8GsJFVLGYXuFF2jJzH8P1rcximLOIJ8UpIKzA5kB7R3qjpbWvpsQ1OitgmdgJtkCoSAZVbeuYGzbMC38H/CZINTqxquU5SEJKBtZDItZ5t8N2lqSGE32XCFKsOYBKv6HXz38e16sJ8IZ3dDnuhL4RKZhjBgFqJuTsb8g7Sn9RvuGGp+m5Y8/0=\""
            buildConfigField "String", "TXT_DOMAIN", "\"zc6bbf5tn,y7ia274c5,5yhewn7jk,ujek24h4s,nsdcd2y56,i6b42foe4,y4ur4wnt7,ofeuchf74,k97uv7tg8,2x8wqpips\""
            buildConfigField "String", "TXT_DUN_TYPE", "\"ero20q.txtalspadw.com\""
            buildConfigField "String", "KEY_KK_DUN", "\"7eOitIIseMHTI3jx2lfYm9jKMZTIl0h4LQoe9teogrz9aZWA7QNSbByhcVmQhs5W\""
            buildConfigField "String", "KEY_ANQUAN_DUN", "\"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\""
            buildConfigField "String", "TRUEWATCH_URL", "\"https://id1-rum-openway.truewatch.com\""
            buildConfigField "String", "TRUEWATCH_TOKEN", "\"5e5371be3c2f4d0e99e3f8afc5976f76\""
            buildConfigField "String", "TRUEWATCH_APPID", "\"com_miyu_android\""
            buildConfigField "boolean", "IS_TEST", "false"
            buildConfigField "boolean", "CAN_SCREENSHOT", "false"
        }
        debugProd {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            debuggable true
            signingConfig signingConfigs.debug
            buildConfigField "String", "AES_KEY_DOMAIN", "\"mfp35NenyXKx7CNp\""
            buildConfigField "String", "AES_VI_DOMAIN", "\"DZJXTTFr100Uycss\""
            matchingFallbacks = ["debug"]
            buildConfigField "String", "KEY_BYTE_DUN_1", "\"fb7fd4ac673efcfc3be64eb541b357e5\""
            buildConfigField "String", "UUID_BYTE_DUN_1", "\"FMLpBXr1nJn8QFxj1IdZ9YAEvp38ZCKLBVd7VMGw1dKm3YgkeltWQp1tYjy/90Wui+hXj5w61hs1XLQngq2RQ8fpRqdxt5UJcBNnPZSeWdS/FszeXGb1LoT3pWJlnv6XgFiYLj6X0LZ4L1lth34w0u5uJ+I7asBIdKYIwXlgO2uwt5rJ0dqCF38KRKJGEpHvPVBAlpkgsnCO3yMyTDJ/6ZLW1e5T5kCzSV8=\""
            buildConfigField "String", "KEY_BYTE_DUN_2", "\"cfc4c4fedecd92f031ffed5a1095da38\""
            buildConfigField "String", "UUID_BYTE_DUN_2", "\"1qSJYhWhRdeuKKgzYH3s/nRd4sBai+fpHXR1sb7wMvMedJ8GsJFVLGYXuFF2jJzH8P1rcximLOIJ8UpIKzA5kB7R3qjpbWvpsQ1OitgmdgJtkCoSAZVbeuYGzbMC38H/CZINTqxquU5SEJKBtZDItZ5t8N2lqSGE32XCFKsOYBKv6HXz38e16sJ8IZ3dDnuhL4RKZhjBgFqJuTsb8g7Sn9RvuGGp+m5Y8/0=\""
            buildConfigField "String", "KEY_KK_DUN", "\"7eOitIIseMHTI3jx2lfYm9jKMZTIl0h4LQoe9teogrz9aZWA7QNSbByhcVmQhs5W\""
            buildConfigField "String", "KEY_ANQUAN_DUN", "\"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\""
            buildConfigField "String", "TXT_DOMAIN", "\"zc6bbf5tn,y7ia274c5,5yhewn7jk,ujek24h4s,nsdcd2y56,i6b42foe4,y4ur4wnt7,ofeuchf74,k97uv7tg8,2x8wqpips\""
            buildConfigField "String", "TXT_DUN_TYPE", "\"ero20q.txtalspadw.com\""
            buildConfigField "String", "TRUEWATCH_URL", "\"https://id1-rum-openway.truewatch.com\""
            buildConfigField "String", "TRUEWATCH_TOKEN", "\"5e5371be3c2f4d0e99e3f8afc5976f76\""
            buildConfigField "String", "TRUEWATCH_APPID", "\"com_miyu_android\""
            buildConfigField "boolean", "IS_TEST", "false"
            buildConfigField "boolean", "CAN_SCREENSHOT", "true"
        }
        alpha {
            debuggable false
            //----以下两项游戏盾需要设置成 false
            minifyEnabled true
            shrinkResources true
            //-----
            signingConfig signingConfigs.debug
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "AES_KEY_DOMAIN", "\"mfp35NenyXKx7CNp\""
            buildConfigField "String", "AES_VI_DOMAIN", "\"DZJXTTFr100Uycss\""
            matchingFallbacks = ["release"]
            buildConfigField "String", "KEY_BYTE_DUN_1", "\"65abaf627a522a4324a83f26e99586bc\""
            buildConfigField "String", "UUID_BYTE_DUN_1", "\"hzgDej0xYuXAFgSHrHqTmw5VCukiz8D8d1k+nTgJ0d8s+LRxWN5oaEd3s3ACsvCgypVhZElbIR9I5btYZc7TfCEhSEqCnFL6rOT20kaxb3ajR+uDfeQHbRzauxI1SkM+YJS0UX9f8guKKcCjxwnVpcI1KAXppCzurI3ojG6cXOtB7Qt7igaAzUpnGjUaL8PkbgwagI3U00vM/kRDce+Q4DGb\""
            buildConfigField "String", "KEY_BYTE_DUN_2", "\"0a1f1ca0d0e2afdb8571d4692e898945\""
            buildConfigField "String", "UUID_BYTE_DUN_2", "\"OSXDABdqSqz7IBuX6k9kn3L/tqxns91xPtGxi1gZZ6f7sYwN0bwAJz1loJ+pffba7i2AOKLdS3QD7WZ00+920iCsQxw7KBEylinJ4h7g06qNnjzfjy57pxrBPFPFLrjzVuoaLX1RR3pAVKqZqNZnRRJq8OftVQMRqLNnQ1eFKxGpnyt/cb60SUCz8QstSc9R8Z5XCSPZ0suoXWm5gfzaxvDPUibLUE8xfl8=\""
            buildConfigField "String", "KEY_KK_DUN", "\"mOmomSWFo6xe5tlhTqiHK0Jg8NJzPgX2zNYI2d1MwbLTH9sLH+g1vyXuZnZB3iPU\""
            buildConfigField "String", "KEY_ANQUAN_DUN", "\"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\""
            buildConfigField "String", "TXT_DOMAIN", "\"zc6bbf5tn,y7ia274c5,5yhewn7jk,ujek24h4s,nsdcd2y56,i6b42foe4,y4ur4wnt7,ofeuchf74,k97uv7tg8,2x8wqpips\""
            buildConfigField "String", "TXT_DUN_TYPE", "\"test.wrknweexay.com\""
            buildConfigField "String", "TRUEWATCH_URL", "\"https://id1-rum-openway.truewatch.com\""
            buildConfigField "String", "TRUEWATCH_TOKEN", "\"5e5371be3c2f4d0e99e3f8afc5976f76\""
            buildConfigField "String", "TRUEWATCH_APPID", "\"com_miyu_android\""
            buildConfigField "boolean", "IS_TEST", "true"
            buildConfigField "boolean", "CAN_SCREENSHOT", "true"
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            debuggable true
            signingConfig signingConfigs.debug
            buildConfigField "String", "AES_KEY_DOMAIN", "\"mfp35NenyXKx7CNp\""
            buildConfigField "String", "AES_VI_DOMAIN", "\"DZJXTTFr100Uycss\""
            buildConfigField "String", "KEY_BYTE_DUN_1", "\"65abaf627a522a4324a83f26e99586bc\""
            buildConfigField "String", "UUID_BYTE_DUN_1", "\"hzgDej0xYuXAFgSHrHqTmw5VCukiz8D8d1k+nTgJ0d8s+LRxWN5oaEd3s3ACsvCgypVhZElbIR9I5btYZc7TfCEhSEqCnFL6rOT20kaxb3ajR+uDfeQHbRzauxI1SkM+YJS0UX9f8guKKcCjxwnVpcI1KAXppCzurI3ojG6cXOtB7Qt7igaAzUpnGjUaL8PkbgwagI3U00vM/kRDce+Q4DGb\""
            buildConfigField "String", "KEY_BYTE_DUN_2", "\"0a1f1ca0d0e2afdb8571d4692e898945\""
            buildConfigField "String", "UUID_BYTE_DUN_2", "\"OSXDABdqSqz7IBuX6k9kn3L/tqxns91xPtGxi1gZZ6f7sYwN0bwAJz1loJ+pffba7i2AOKLdS3QD7WZ00+920iCsQxw7KBEylinJ4h7g06qNnjzfjy57pxrBPFPFLrjzVuoaLX1RR3pAVKqZqNZnRRJq8OftVQMRqLNnQ1eFKxGpnyt/cb60SUCz8QstSc9R8Z5XCSPZ0suoXWm5gfzaxvDPUibLUE8xfl8=\""
            buildConfigField "String", "KEY_KK_DUN", "\"mOmomSWFo6xe5tlhTqiHK0Jg8NJzPgX2zNYI2d1MwbLTH9sLH+g1vyXuZnZB3iPU\""
            buildConfigField "String", "KEY_ANQUAN_DUN", "\"NDBjNjYwMDQyNWZkZTAyZmJhMTliOWE2ZjBjZDAxYjd88HNgLQEmPDYPoVrW+fkmAgpsQSr7HCoiLMT3NcjvkwcIKr6dpN30HbEZleKnWL7CmIeNeKC7ymF0hgQRRR5GOznyWRntTx8k2u2j0xcusqIHzmimkd8e7Um6gDJpzF9IZgpj7+mV/m9gyg/DG0QPhhrrCbdcp5yCISe5ULEEWVfq1asKC2b5IhItHyTHYp3T305XqxsR9F3KvnqZoJPDOaWWnPHg8j3vENhdINgnLdpH6td0+1+Iavd95fKw8lRZikvxskdSfX0/V63OPirGZXfDVoSWZusuJ+yVIQ73FSu7dJ9leqvo+JHbvGSii8qnfAe4KXBlSAebPuXGO/0i59HinoCkLDxElMxLSRt/cuo3Zirf9RIEMRqwcePeEoE4s/iWxKCSpveWb8ivAFm1W4CYm1TuaRyTi3o6dknIlVXD6FbxBCTtnIF38r65dH6GDKXPLvlN930a+y1+JJblh9HlGZ7NvA0e3gjW630Dt+QdLn1+ZxlCCuHEUfVulleVX4aj/vBMQlCzaAM/oo5h6IhkfScDS9Fr3eeZ9sbxO0Iqq3+HkCDBQUvzSyem8Yk+TkP+C237QinzhZkDMUx02G6nT9cqiuj3t2DpeiNNXf1hLhmbzLXjfkk9IvPOmRzewoTFGljA9oNsVgVmLXbAgfGhJIi2V6FeB+q4ccy3uqidt7Jtf10aBk8UHMXmgSfzHsONtKSyMYu6s5mHqqNWLv8rBjqgwJbxcskTyRgv9j3OhlpjbSZMrAC+vQB4fQzB/8hRpactM0nzvcB4KyAnKNMsS5+6aeI7Rwu7j2W1YXX/+gsq0XZ0P4jk5mJbUG4pEmSDPppKwDz7sWFwfJGMbTdLJfzrh+FmJgpj6515cacjW4E5vKJDqTAQU6dHtCswjOgqXOpK3MKItPtWAYhxATSd60F7lXJF5rt5aU8LRQ2+zXJmQ+cX9fgVBPUqRICb7HifE32n+2/4B/xa54qL+k7g8JHAVNsDKC3klwUwch/yYPrJx3vbgGbUzl7blQbd38xyH94ohBun+21KGfX4wr+LhJ0mkiEYELVU+ZrIdJRy7x7vMVuikO6Ei/0FZDWNK1lQA6OyojDHt1t96t+ycUoUEkVEzcX/J0reEAaFaZtGJ3orNchQ1fTXvyDgkXYMoX5LOFtHgim2i50e3lVdyyt/OLJl4unRCslol3Z5ZoCrHLSkoYylycZi/ZKiEguUaW9Cv+ABcGtEApkr6Y21wTdEm6Wgj9wMVQhnoQ2kQ0t5+Gp3qDQjJQJqSPe7PF9aLh86nl0s1VIH0mIpXo9ehXoYQu/TIvEROeDfVKutVkao/P5l67Am5wNfVx6p8S0PUsa3VBTFPK+uZPgMR5tndNVTo0scYD/PF45o0pdsfNRY4wOh/yxiIoII8rgQbOebJIfJUJMBF1jlivyupfBcrPZwYHG82X+uJ8Ib7awB3Mr4jXeYyQ7crPwHTvXEcSA5oTSBsvKd6nSChUxcA14LM+iK0wSGcoz7BUwGNwkvKIcFhYecFcOHu71pYk3lV7RhsPCS4AIIz3tFMLzOFNqa109Nda9M/evxeAWSwIafl5PqUTqD+ggej1Iy9W4o1l63vWMFXgdwTsUO0F6ltbfeOeJV5WWQO0f2Wz6JXDsIfk+r3onlfu8i6DbzKunoPKhcAv4TztIvnuZSO5krr5xAiIA4S09CAbYwrUrg+VbKbIoMKnB4IKpMhZzfS9JFECQvdGFEusIURUJpUgs0MH2oxx20CJyQ+ZMmKV55eP3T5jmjgex6T/tlFzRT/5+2kWOhTHMyB0lGjqawIHro2f+12uZyUNRuajQzIuFuXbG4H8irVPG9KAoKgA6M5NZvUrEYWFGs5TjUgB8yMT9rIz1OjHBH9xHnEWUlsHp3WeegvTn7rwD+s4GsuKBR7HnXvXLMm50CGR+zeQ4lwcUZovgJt6WGxCYDA2GIuUf0CO7OyCc3DHpFWJHA87KqAlRrZvy+qFTxX3ioXjxh7MHixd5axtgu1f9aQT1J5ftTPBWkme3yBd1OnwgbiPa3fCoGfBw7dy3VCrlsGwwxGCI7JXFTGed0hRa7tJ1/mGrhs+kTugKeFguM0Dv/4DV+CEW6WnaIyw9ugAHlEqxEtr7SnPQ8YQUPXc+qGHSu1VTZs6i/2469AWmIukIVx3P3KQttYfx5myYE/iu1TUkPIV9M25eScgFi0G1TMm7uhpcclaZLuYHv+2fYwLBct8YZpP5JNXoJN6Tk88rP5yYhtw9VunXlpUfI6KhaW6IXXExvOS6HKEkNzXl0qFd74W/zgexDx/rO/6iQnlMrNL8ZZYMEpGYEjaE5tBotxDCyum7Z23OKnADHcjepjRmzpAaZKwt+Gi5rH/wTrV996/6e+aHIhHnT0mLcf2dLjj6yJK2lbPZeDiezAyJWB6bzBkP0XGjg2c2jU3w79p5kw0i8EQPBrkJJiec/gSfNWrW+1oIacFRD9AdKmWay1r1MOjLZzWRGevoVEut/LOr6XPo5NjapS0W9Ut1qd9zhB/7v5yGfB3av4FNF0QPzVTb9/J5zUdL1IN5qhlGLyv7K0/6xycWPehSQ+rBTYZZPrABYJO89f4wTS4KjrPpAK4UJ9IdcW5jPe+Az8b3SsgcjsPL9PB4lyDXG9zNdNfHokkG7pQ0AuometoM3hPLgKjcL9NdYqgzm5/7GyW/ic5plbQbzIf1JgaSSraAjIzy8TU+CrixoFsSvXW45Ffct1ByxZwGYXs7fsJPa12LILNgqHWiD6NBBD4fO8D3c4xrCqo5nVUTUWGKNAt6/3aSHLzgGity6N8LFlPB0LaMZSsQF04LRYS9tbvLKGK4sknzPHV9xSQu+Hzf+7KjNFg4qE8S/3oR+mwfmhdnUznJpz5WmtVnz9FwvRldPAW/EVdIpflVEVYGne/WLRQ86SFVkbi+i6lEMIHIMX2xICxRUOwf8z+4h39cNsO2r6E/y+DpiN0k67++s9je2MVqANy1T4204yFwXROchq4W3l5Hi/NR3/iaWPoT6lWKeshbVQkaAi+S7wAsPpEeoHACePjfp7mvj9aM4kh6sBbLz3Kpj3rMS1NErFTPz4gimPOkurctvtwutGJK4x2J6mKhQxAsI+tBeQAKnkBSYarSMrNx5aTSGblNaRt9SebHwjFttPkfc2C9D2U4xa3FBSmB5hrR95lUPJ2+VVQQaMyVME+tI8lkZMQcOkWtx2gFWaAMbTh7oB/CfbmOl2PqP3xTipY6tLwDhm6oKPYb4prAB+PH1h9/cy3Zn7+zWlr+m22Iq/xwJEdaPPRuhR+t0xiVGJkTUiDLCurexKKJs2Q+PXBN6rVRy09/OhQ==\""
            buildConfigField "String", "TXT_DOMAIN", "\"zc6bbf5tn,y7ia274c5,5yhewn7jk,ujek24h4s,nsdcd2y56,i6b42foe4,y4ur4wnt7,ofeuchf74,k97uv7tg8,2x8wqpips\""
            buildConfigField "String", "TXT_DUN_TYPE", "\"test.wrknweexay.com\""
            buildConfigField "String", "TRUEWATCH_URL", "\"https://id1-rum-openway.truewatch.com\""
            buildConfigField "String", "TRUEWATCH_TOKEN", "\"5e5371be3c2f4d0e99e3f8afc5976f76\""
            buildConfigField "String", "TRUEWATCH_APPID", "\"com_miyu_android\""
            buildConfigField "boolean", "IS_TEST", "true"
            buildConfigField "boolean", "CAN_SCREENSHOT", "true"
        }
    }

    sourceSets {
        main { jniLibs.srcDirs = ['libs', 'src/main/jniLibs', 'jniLibs'] }
        miyu10013 { jniLibs.srcDirs = ['libs', 'src/main/jniLibs', 'jniLibs'] }
        yaoai10131 {
            res.srcDirs = ['src/yaoai10131/res', 'src/yaoai10131/res/']
            assets.srcDirs = ['src/yaoai10131/assets']
        }
        daji10127 {
            res.srcDirs = ['src/daji10127/res', 'src/daji10127/res/']
            assets.srcDirs = ['src/daji10127/assets']
        }
        yingtao10237 {
            res.srcDirs = ['src/yingtao10237/res', 'src/yingtao10237/res/']
            assets.srcDirs = ['src/yingtao10237/assets']
        }
        madou10240 {
            res.srcDirs = ['src/madou10240/res', 'src/madou10240/res/']
            assets.srcDirs = ['src/madou10240/assets']
        }
        liujiu10242 {
            res.srcDirs = ['src/liujiu10242/res', 'src/liujiu10242/res/']
            assets.srcDirs = ['src/liujiu10242/assets']
        }
        jiali10507 {
            res.srcDirs = ['src/jiali10507/res', 'src/jiali10507/res/']
            assets.srcDirs = ['src/jiali10507/assets']
        }
        douyin10510 {
            res.srcDirs = ['src/douyin10510/res', 'src/douyin10510/res/']
            assets.srcDirs = ['src/douyin10510/assets']
        }
        luoli10817 {
            res.srcDirs = ['src/luoli10817/res', 'src/luoli10817/res/']
            assets.srcDirs = ['src/luoli10817/assets']
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
    }

    buildFeatures {
        viewBinding true
    }

    // Enable Android App Bundle optimizations
    bundle {
        language {
            enableSplit = true
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }

    flavorDimensions "live"
    productFlavors.all {
        ext.outputName = null
    }
    productFlavors {
        livePlayer10013 {
            applicationId "djnsr2025.pcqat.com"
            outputName = "MiYu_10013"
            manifestPlaceholders = [
                    "OP_KEY": "ldlc7r6v",
                    "CHANNEL_ID" : "10013",
                    "BUGLY_APPID": "a24038ddbf"
            ]
        }
        live10013 {
            applicationId "newtu.aililetechnology.com"
            outputName = "MiYu_10013"
            manifestPlaceholders = [
                    "OP_KEY": "ldlc7r6v",
                    "CHANNEL_ID" : "10013",
                    "BUGLY_APPID": "a24038ddbf"
            ]
        }
        miyu10013 {
            applicationId "xwu0605.aquafinamo.vmj90013.rspbe"
            outputName = "MiYu_10013"
            manifestPlaceholders = [
                    "OP_KEY": "xzbtb8sp",
                    "CHANNEL_ID" : "10013",
                    "BUGLY_APPID": "a24038ddbf"
            ]
        }
        luoli10817 {
            applicationId "klb0605.aquafinamo.vll90817.vmshr"
            outputName = "LuoLi_10817"
            manifestPlaceholders = [
                    "OP_KEY": "kah0k3t4",
                    "CHANNEL_ID" : "10817",
                    "BUGLY_APPID": "69b3dd0872"
            ]
        }
        yaoai10131 {
            applicationId "ufm0605.aquafinamo.vya90131.qjbit"
            outputName = "YaoAi_10131"
            manifestPlaceholders = [
                    "OP_KEY": "qcwmbnh4",
                    "CHANNEL_ID" : "10131",
                    "BUGLY_APPID": "bd08c6ea05"
            ]
        }
        daji10127 {
            applicationId "djl0605.aquafinamo.vdj90127.imlcu"
            outputName = "DaJi_10127"
            manifestPlaceholders = [
                    "OP_KEY": "kfi3aqix",
                    "CHANNEL_ID" : "10127",
                    "BUGLY_APPID": "75d22ee57e"
            ]
        }
        yingtao10237 {
            applicationId "nox0605.aquafinamo.vyt90237.efnvf"
            outputName = "YingTao_10237"
            manifestPlaceholders = [
                    "OP_KEY": "hgazau9h",
                    "CHANNEL_ID" : "10237",
                    "BUGLY_APPID": "bb62fdb99c"
            ]
        }
        madou10240 {
            applicationId "uak0605.aquafinamo.vmd90240.kguvp"
            outputName = "MaDou_10240"
            manifestPlaceholders = [
                    "OP_KEY": "i47ssdwc",
                    "CHANNEL_ID" : "10240",
                    "BUGLY_APPID": "b752182272"
            ]
        }
        liujiu10242 {
            applicationId "ena0605.aquafinamo.vlj90242.aktqb"
            outputName = "69ZhiBo_10242"
            manifestPlaceholders = [
                    "OP_KEY": "xap4eo3q",
                    "CHANNEL_ID" : "10242",
                    "BUGLY_APPID": "9b0b71063f"
            ]
        }
        jiali10507 {
            applicationId "cwx0605.aquafinamo.vjl90507.pfxau"
            outputName = "JiaLi_10507"
            manifestPlaceholders = [
                    "OP_KEY": "prgchr7r",
                    "CHANNEL_ID" : "10507",
                    "BUGLY_APPID": "2b0c597184"
            ]
        }
        douyin10510 {
            applicationId "wyf0605.aquafinamo.vdy90510.xdpnp"
            outputName = "DouYin_10510"
            manifestPlaceholders = [
                    "OP_KEY": "omjjg3si",
                    "CHANNEL_ID" : "10510",
                    "BUGLY_APPID": "4fb8aefa79"
            ]
        }
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            def suff = "TEST"
            if (variant.buildType.name == "release") {
                suff = "PROD"
            }
//            def newName = "${variant.productFlavors[0].outputName}_${suff}_${variant.productFlavors[0].applicationId}_${releaseTime()}.apk"
            def newName = "${variant.productFlavors[0].outputName}_${suff}_${variant.versionName}_${releaseTime()}.apk"
            outputFileName = newName
        }
    }

    repositories {
        flatDir {
            dirs 'libs', '../libs'
        }
    }

    gradle.taskGraph.whenReady {
        tasks.each { task ->
            if (task.name.contains("uploadCrashlyticsMappingFile")) {
                task.enabled = false
            }
        }
    }
}

static def releaseTime() {
    return new Date().format("MMddHH", TimeZone.getDefault())
}

configurations.all {
    resolutionStrategy {
        force 'androidx.core:core-ktx:1.8.0'
        force 'androidx.core:core:1.8.0'
        force 'androidx.appcompat:appcompat:1.6.1'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    //直播
    implementation project(':main')
    //阿里 ARouter
    implementation rootProject.ext.dependencies["arouter"]
    kapt rootProject.ext.dependencies["arouter-compiler"]
    implementation rootProject.ext.dependencies["multidex"]

    //bugly
    implementation 'com.tencent.bugly:crashreport:4.1.9.3'
}
