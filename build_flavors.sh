#!/bin/bash

# Configuration file path
CONFIG_FILE="flavor_config.txt"
BUILD_TYPE="Release" # Default build type Release / Debug
OUTPUT_DIR="build_output"
GRADLE_TASK="assemble"  # Default gradle task

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Config file not found. Creating a sample config file..."
    cat > "$CONFIG_FILE" << EOL

EOL
    echo "Sample config file created at $CONFIG_FILE. Please edit it with your flavor names."
    exit 1
fi

# Process command line options
while getopts "t:o:d" opt; do
  case $opt in
    t)
      BUILD_TYPE="$OPTARG"
      ;;
    o)
      OUTPUT_DIR="$OPTARG"
      ;;
    d)
      GRADLE_TASK="bundle"  # Use bundle for AAB (Android App Bundle)
      echo "Building Android App Bundles instead of APKs"
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      exit 1
      ;;
  esac
done

# Display build info
echo "🚀 Starting automated build process"
echo "📦 Build type: $BUILD_TYPE"
echo "📂 Output directory: $OUTPUT_DIR"
echo "⚙️ Gradle task: $GRADLE_TASK"
echo "--------------------------------------"

# Read flavors from config file
echo "Reading flavor configuration from $CONFIG_FILE..."
cat "$CONFIG_FILE"
echo "--------------------------------------"

# Count lines in the file (excluding comments and empty lines)
total_flavors=$(grep -v "^#" "$CONFIG_FILE" | grep -v "^$" | wc -l)
echo "Total flavors found in config: $total_flavors"

# Try different methods to read the file
echo "Using primary method to read flavors..."
mapfile -t FLAVORS < <(grep -v "^#" "$CONFIG_FILE" | grep -v "^$")

# If mapfile is not available or failed, use alternative
if [ ${#FLAVORS[@]} -eq 0 ]; then
    echo "Primary method returned 0 flavors, trying alternative method..."
    # Read the file line by line
    FLAVORS=()
    while IFS= read -r line; do
        # Skip empty lines and comments
        if [[ -n "$line" && ! "$line" =~ ^# ]]; then
            FLAVORS+=("$line")
            echo "Added flavor: $line"
        fi
    done < "$CONFIG_FILE"
fi

# Third fallback method if needed
if [ ${#FLAVORS[@]} -eq 0 ]; then
    echo "Both methods failed. Using basic method..."
    # Use basic method
    FLAVORS=($(grep -v "^#" "$CONFIG_FILE" | grep -v "^$"))
fi

# Print all flavors that will be processed
echo "📋 Flavors to build (${#FLAVORS[@]} total):"
for ((i=0; i<${#FLAVORS[@]}; i++)); do
    echo " - $((i+1)): ${FLAVORS[$i]}"
done
echo "--------------------------------------"

# Initialize counters
success_count=0
failed_count=0
failed_flavors=""
start_time=$(date +%s)

# Process each flavor
for flavor in "${FLAVORS[@]}"; do
    echo "🔄 Building flavor: $flavor"
    
    # Construct the Gradle task name without any spaces
    gradle_cmd="${GRADLE_TASK}${flavor}${BUILD_TYPE}"
    
    echo "Running Gradle task: $gradle_cmd"
    
    # Execute the gradle build command
    ./gradlew "$gradle_cmd"
    
    build_result=$?
    if [ $build_result -eq 0 ]; then
        echo "✅ Successfully built $flavor"
        
        # Find and copy the APK/AAB to the output directory
        if [ "$GRADLE_TASK" == "assemble" ]; then
            # Convert to lowercase using tr
            flavor_lower=$(echo "$flavor" | tr '[:upper:]' '[:lower:]')
            build_type_lower=$(echo "$BUILD_TYPE" | tr '[:upper:]' '[:lower:]')
            
            # Find the flavor output directory
            flavor_output_dir="./app/build/outputs/apk/$flavor_lower/$build_type_lower"
            
            if [ -d "$flavor_output_dir" ]; then
                # Get the first APK file in the directory
                first_apk=$(find "$flavor_output_dir" -name "*.apk" | head -n 1)
                
                if [ -n "$first_apk" ]; then
                    # Get only the filename without path
                    apk_filename=$(basename "$first_apk")
                    # Copy with flavor name as prefix
                    cp "$first_apk" "$OUTPUT_DIR/${flavor}_${apk_filename}"
                    echo "📦 Copied to $OUTPUT_DIR/${flavor}_${apk_filename}"
                    success_count=$((success_count + 1))
                else
                    echo "⚠️ No APK found for $flavor in $flavor_output_dir"
                    failed_count=$((failed_count + 1))
                    failed_flavors="$failed_flavors $flavor"
                fi
            else
                # Try a more general search approach as fallback
                echo "⚠️ Directory $flavor_output_dir not found. Trying general search..."
                first_apk=$(find ./app/build/outputs/apk -name "*${flavor}*${BUILD_TYPE}*.apk" | head -n 1)
                
                if [ -n "$first_apk" ]; then
                    # Get only the filename without path
                    apk_filename=$(basename "$first_apk")
                    # Copy with flavor name as prefix
                    cp "$first_apk" "$OUTPUT_DIR/${flavor}_${apk_filename}"
                    echo "📦 Copied to $OUTPUT_DIR/${flavor}_${apk_filename}" 
                    success_count=$((success_count + 1))
                else
                    echo "⚠️ No APK found for $flavor"
                    failed_count=$((failed_count + 1))
                    failed_flavors="$failed_flavors $flavor"
                fi
            fi
        else
            # For bundles
            first_bundle=$(find ./app/build/outputs/bundle -name "*${flavor}*${BUILD_TYPE}*.aab" | head -n 1)
            
            if [ -n "$first_bundle" ]; then
                # Get only the filename without path
                bundle_filename=$(basename "$first_bundle")
                # Copy with flavor name as prefix
                cp "$first_bundle" "$OUTPUT_DIR/${flavor}_${bundle_filename}"
                echo "📦 Copied to $OUTPUT_DIR/${flavor}_${bundle_filename}"
                success_count=$((success_count + 1))
            else
                echo "⚠️ No Bundle found for $flavor"
                failed_count=$((failed_count + 1))
                failed_flavors="$failed_flavors $flavor"
            fi
        fi
    else
        echo "❌ Failed to build $flavor"
        failed_count=$((failed_count + 1))
        failed_flavors="$failed_flavors $flavor"
    fi
    
    echo "--------------------------------------"
done

end_time=$(date +%s)
duration=$((end_time - start_time))
minutes=$((duration / 60))
seconds=$((duration % 60))

# Display summary
echo "📊 Build Summary"
echo "⏱️ Total time: ${minutes}m ${seconds}s"
echo "✅ Successful builds: $success_count"
echo "❌ Failed builds: $failed_count"

if [ $failed_count -gt 0 ]; then
    echo "Failed flavors:$failed_flavors"
fi

echo "📁 Output files saved to: $OUTPUT_DIR"