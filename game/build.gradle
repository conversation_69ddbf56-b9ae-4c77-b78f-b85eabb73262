plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }
    aaptOptions {
        // Enable cruncher for better PNG optimization
        cruncherEnabled = true
        // useNewCruncher is deprecated and always enabled now
        noCompress "wav,mp3"  //表示不让aapt压缩的文件后缀
    }
    buildTypes {
        release {
            minifyEnabled true
            // Note: shrinkResources not available for library modules
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
    }
}

//kapt {
//    generateStubs = true
//    arguments {
//        arg("AROUTER_MODULE_NAME", project.getName())
//    }
//}
repositories {
    flatDir {
        dirs 'libs','../libs'
    }
}
dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    //common
    api project(':common')

    //阿里 ARouter
    implementation rootProject.ext.dependencies["arouter"]
    implementation 'androidx.core:core-ktx:1.8.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    kapt rootProject.ext.dependencies["arouter-compiler"]
}
