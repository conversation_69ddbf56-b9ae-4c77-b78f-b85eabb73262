<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvLotteryContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="5"
        tools:listitem="@layout/item_cur_expect" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvLotteryResult"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvLotteryContent"
        tools:itemCount="3"
        tools:listitem="@layout/item_cur_expect" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupCommon"
        app:constraint_referenced_ids="rvLotteryContent, rvLotteryResult"
        android:layout_width="wrap_content"
        android:visibility="gone"
        tools:visibility="gone"
        android:layout_height="wrap_content"/>
    <!--左-->
    <com.hjq.shape.layout.ShapeConstraintLayout
        android:id="@+id/clCowLeft"
        android:layout_width="wrap_content"
        android:layout_height="38dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/vSpace"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="6dp"
        app:shape_radius="@dimen/dp7"
        app:shape_solidGradientEndColor="@color/c_26"
        app:shape_solidGradientStartColor="@color/c_19"
        app:shape_strokeColor="@color/c_a0"
        app:shape_strokeSize="1dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_cow_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:itemCount="3"
            tools:listitem="@layout/item_cur_expect" />

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvCowLeft"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp20"
            android:gravity="center"
            android:textColor="@color/c_3f"
            android:textSize="@dimen/text_size_24"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:shape_solidGradientCenterColor="@color/color_80000000"
            app:shape_solidGradientEndColor="@color/transparent"
            app:shape_solidGradientStartColor="@color/transparent"
            tools:text="@string/app_name" />

    </com.hjq.shape.layout.ShapeConstraintLayout>

    <!--右-->
    <com.hjq.shape.layout.ShapeConstraintLayout
        android:id="@+id/clCowRight"
        android:layout_width="wrap_content"
        android:layout_height="38dp"
        android:layout_marginStart="5dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/vSpace"
        app:layout_constraintTop_toTopOf="@+id/clCowLeft"
        app:shape_radius="@dimen/dp7"
        app:shape_solidGradientEndColor="@color/c_87"
        app:shape_solidGradientStartColor="@color/c_55"
        app:shape_strokeColor="@color/c_ff80"
        app:shape_strokeSize="1dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_cow_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:itemCount="3"
            tools:listitem="@layout/item_cur_expect" />

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvCowRight"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp20"
            android:gravity="center"
            android:textColor="@color/c_ff7"
            android:textSize="@dimen/text_size_24"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:shape_solidGradientCenterColor="@color/color_80000000"
            app:shape_solidGradientEndColor="@color/transparent"
            app:shape_solidGradientStartColor="@color/transparent"
            tools:text="@string/app_name" />

    </com.hjq.shape.layout.ShapeConstraintLayout>

    <View
        android:id="@+id/vSpace"
        android:layout_width="2dp"
        android:layout_height="1dp"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/clCowRight"
        app:layout_constraintStart_toEndOf="@+id/clCowLeft"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivLeftWin"
        android:layout_width="23dp"
        android:layout_height="23dp"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_cow_win"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/vSpace"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivRightWin"
        android:layout_width="23dp"
        android:layout_height="23dp"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_cow_win"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupNN"
        app:constraint_referenced_ids="clCowLeft, clCowRight, vSpace, ivLeftWin, ivRightWin"
        android:layout_width="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_height="wrap_content"/>
</androidx.constraintlayout.widget.ConstraintLayout>