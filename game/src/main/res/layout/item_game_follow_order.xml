<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linTypeInfo"
    android:layout_width="match_parent"
    android:layout_height="29dp"
    android:divider="@color/skin_color_lottery_dialog_stroke"
    android:gravity="center_vertical"
    android:showDividers="middle"
    app:layout_constraintTop_toBottomOf="@+id/clInfo">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="3"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvIssue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="4"
            tools:listitem="@layout/item_game_follow_order_issue" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvPlayMethod"
        style="@style/style_game_follow_order_tip"
        tools:text="@string/game_his_how_to_play" />

    <TextView
        android:id="@+id/tvBets"
        style="@style/style_game_follow_order_tip"
        android:layout_weight="2"
        android:textColor="#05f9bd"
        tools:text="@string/bets" />

    <TextView
        android:id="@+id/tvResult"
        style="@style/style_game_follow_order_tip"
        android:layout_weight="2"
        android:textColor="@color/skin_color_blue_red_sel"
        tools:text="@string/result" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="4"
        android:gravity="center">

        <ImageView
            android:id="@+id/ivFollow"
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            android:src="@drawable/skin_icon_bet_follow"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivContraryFollow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivContraryFollow"
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            android:src="@drawable/skin_icon_bet_contrary_follow"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivFollow"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>