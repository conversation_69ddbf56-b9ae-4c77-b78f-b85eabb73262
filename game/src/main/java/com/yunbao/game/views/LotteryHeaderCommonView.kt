package com.yunbao.game.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.yunbao.common.bean.WsResult
import com.yunbao.common.custom.ItemDecoration
import com.yunbao.common.utils.click
import com.yunbao.common.utils.invisible
import com.yunbao.common.utils.loadImg
import com.yunbao.common.utils.visibility
import com.yunbao.common.widget.CountdownView
import com.yunbao.game.R
import com.yunbao.game.adapter.LastPeriodAdapter
import com.yunbao.game.bean.GameLocalStyleInfo
import com.yunbao.game.databinding.ViewLotteryHeaderCommonBinding
import com.yunbao.game.util.GameDataUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.plus

/**
 * 通用开奖头部
 */
class LotteryHeaderCommonView @JvmOverloads constructor(context: Context, attr: AttributeSet? = null) :
    ConstraintLayout(context, attr) {
    private val coroutineScope = (this.findViewTreeLifecycleOwner()?.lifecycleScope
        ?: CoroutineScope(SupervisorJob())).plus(Dispatchers.Main)

    //当前开奖内容 Current lottery content
    private var lotteryContentAdapter: LastPeriodAdapter
    private val lotteryContentList = mutableListOf<GameLocalStyleInfo>()

    //当前开奖内容对应结果 Corresponding results of the current lottery content
    private var lotteryResultAdapter: LastPeriodAdapter
    private val lotteryResultList = mutableListOf<GameLocalStyleInfo>()

    private var gameId = 0

    private val bind by lazy {
        ViewLotteryHeaderCommonBinding.inflate(LayoutInflater.from(context), this, true)
    }

    init {
        lotteryContentAdapter = LastPeriodAdapter(lotteryContentList)
        bind.rvLotteryContent.apply {
            adapter = lotteryContentAdapter
            layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            setHasFixedSize(true)
            itemAnimator = DefaultItemAnimator()
            val decoration = ItemDecoration(context, 0x00000000, 4f, 4f).apply {
                isOnlySetItemOffsetsButNoDraw = true
                isDrawBorderLeftAndRight = true
            }
            addItemDecoration(decoration)
        }

        lotteryResultAdapter = LastPeriodAdapter(lotteryResultList)
        bind.rvLotteryResult.apply {
            adapter = lotteryResultAdapter
            layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            setHasFixedSize(true)
            itemAnimator = DefaultItemAnimator()
            val decoration = ItemDecoration(context, 0x00000000, 4f, 4f).apply {
                isOnlySetItemOffsetsButNoDraw = true
                isDrawBorderLeftAndRight = true
            }
            addItemDecoration(decoration)
        }
    }

    /**
     * 初始化基础配置
     */
    fun initBase(gameId: Int, countdownViewCallback: CountdownView.CountdownViewCallback, logoClick: ()->Unit, followOrderClick: ()->Unit) {
        this.gameId = gameId
        //倒计时
        bind.countdownView.apply {
            setLeftTip(R.string.closing_deadline, R.string.closed_period)
            setCallback(countdownViewCallback)
        }
        bind.ivGameLogo.click(800) {
            logoClick.invoke()
        }
        bind.tvFollowOrder.click {
            followOrderClick.invoke()
        }
    }

    /**
     * 设置数据
     */
    fun setData(gameId: Int, wsResult: WsResult) {
        this.gameId = gameId
        val endTime = wsResult.endTime
        val serviceTime = wsResult.serverTime
        val gameDuration = wsResult.gameDuration
        val endSecond = wsResult.endSecond
        val leave = endTime - serviceTime
        val closeDuration = (gameDuration - endSecond).toLong()
        bind.tvFollowOrder.visibility(gameId != GameDataUtil.TYPE_YXX)
        bind.ivGameLogo.loadImg(wsResult.gameInfo?.icon)
        bind.countdownView.setTime(leave = leave, closeDuration = closeDuration)
        lotteryContentList.clear()
        lotteryResultList.clear()
        //开奖数据
        if (!wsResult.lastPeriod?.res.isNullOrBlank()) {
            wsResult.lastPeriod?.let { lp ->
                val contentList = GameDataUtil.getGameLotteryContentStyle(
                    gameId,
                    lp.res
                )
                if (contentList.isNotEmpty()) {
                    bind.rvLotteryContent.visibility(true)
                    lotteryContentList.addAll(contentList)
                } else {
                    bind.rvLotteryContent.visibility(false)
                }
                val resultList = GameDataUtil.getGameLotteryResultStyle(
                    gameId,
                    lp.resTips
                )
                if (resultList.isNotEmpty()) {
                    bind.rvLotteryResult.visibility(true)
                    lotteryResultList.addAll(resultList)
                } else {
                    bind.rvLotteryResult.visibility(false)
                }
            }
        }
        lotteryContentAdapter.notifyDataSetChanged()
        lotteryResultAdapter.notifyDataSetChanged()
    }

    /**
     * 展示开奖动画
     */
    fun showLotteryAnim() {
        lotteryContentList.clear()
        lotteryContentList.addAll(GameDataUtil.getGameLotteryAnimContentStyle(gameId))
        bind.rvLotteryResult.invisible(true)
        bind.rvLotteryContent.visibility(true)
        lotteryContentAdapter.notifyDataSetChanged()
    }

    /**
     * 获取当前倒计时
     */
    fun getCountdown() = bind.countdownView.getCountdown()

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        coroutineScope.cancel()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        setMeasuredDimension(widthMeasureSpec, heightMeasureSpec)
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }
}