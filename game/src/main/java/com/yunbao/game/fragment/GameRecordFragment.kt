package com.yunbao.game.fragment

import android.os.Bundle
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.listener.OnItemChildClickListener
import com.yunbao.common.Constants
import com.yunbao.common.RoutePath
import com.yunbao.common.reactivehttp.base.BaseRectFragment
import com.yunbao.common.utils.emptyView
import com.yunbao.common.utils.initFooterView
import com.yunbao.common.utils.nav2Act
import com.yunbao.game.R
import com.yunbao.game.adapter.GameRecordAdapter
import com.yunbao.game.databinding.FragmentGameRecordBinding
import com.yunbao.game.reactivehttp.viewmodel.GameVM

/**
 *
 */
class GameRecordFragment : BaseRectFragment() {
    private var type = 0
    private lateinit var recordAdapter: GameRecordAdapter

    override val bind by getBind<FragmentGameRecordBinding>()

    override fun initView() {
        recordAdapter = GameRecordAdapter(mutableListOf())
        recordAdapter.emptyView()
        recordAdapter.addFooterView(initFooterView())
        recordAdapter.setOnItemChildClickListener(recordItemClickListener)
        bind.rvContent.apply {
            adapter = recordAdapter
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(requireContext())
            itemAnimator = DefaultItemAnimator()
        }

        bind.refreshLayout.setOnRefreshListener {
            vm.getGameRecord(type)
        }
    }

    override fun initData() {
        arguments?.let {
            type = it.getInt(Constants.TYPE)
        }
        bind.refreshLayout.autoRefresh()
    }

    private val vm by getViewModel<GameVM> {
        gameRecord.observe(this@GameRecordFragment) {
            bind.tvBottomPourTotalAmount.text = "${it.totalPoint}"
            bind.tvWinPrizeTotalAmount.text = "${it.totalRePoint}"
            val list = it.recordList?.toMutableList() ?: mutableListOf()
            recordAdapter.setNewInstance(list)
            if (list.isNotEmpty()) {
                bind.rvContent.smoothScrollToPosition(0)
            }
            recordAdapter.isUseEmpty = list.isEmpty()
        }
        requestFinish.observe(this@GameRecordFragment) {
            bind.refreshLayout.finishRefresh()
        }
    }

    private val recordItemClickListener =
        OnItemChildClickListener { adapter, view, position ->
            val item = recordAdapter.data[position]
            when (view.id) {
                R.id.tvMore -> {
                    val bundle = Bundle()
                    bundle.putInt("platform_id", item.id)
                    bundle.putInt("type", type + 1)
                    if (type == 0) {
                        bundle.putString("dayType", "今天")
                    } else if (type == 1) {
                        bundle.putString("dayType", "昨日")
                    }
                    nav2Act(RoutePath.PATH_BETTING_LIST, bundle)
                }
            }
        }

    companion object {
        @JvmStatic
        fun newInstance(dayType: Int) =
            GameRecordFragment().apply {
                arguments = Bundle().apply {
                    putInt(Constants.TYPE, dayType)
                }
            }
    }
}