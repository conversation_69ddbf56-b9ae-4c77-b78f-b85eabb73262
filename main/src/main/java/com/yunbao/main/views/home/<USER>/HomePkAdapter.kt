package com.yunbao.main.views.home.hot

import android.annotation.SuppressLint
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.yunbao.common.bean.LiveChannel
import com.yunbao.common.bean.LiveListBean
import com.yunbao.common.bean.NewBannerBean
import com.yunbao.main.bean.CategoryBean
import com.yunbao.main.utils.alphaAnim
import com.yunbao.main.views.home.share.BannerViewHolder
import com.yunbao.main.views.home.share.CategoryListViewHolder
import com.yunbao.main.views.home.share.LiveRoomViewHolder
import com.yunbao.main.views.home.share.PKRoomViewHolder

class HomePkAdapter(
    private val listener: ItemClickListener
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    // 预加载回调
    var onPreload: (() -> Unit)? = null

    // 预加载偏移量
    var preloadItemCount = 3

    // 列表滚动状态
    private var scrollState = RecyclerView.SCROLL_STATE_IDLE

    // 增加预加载状态标记位
    var isPreloading = false

    private var selectedCategory: CategoryBean? = null
    private val currentList = mutableListOf<HomePkItem>()

    enum class ViewType(var numOfItemInRow: Int) {
        CATEGORY_LIST(1),
        BANNERS(1),
        LIVE_ROOM(2),
        PK_ROOM(1),
        EMPTY(1),
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ViewType.CATEGORY_LIST.ordinal -> CategoryListViewHolder.create(
                parent,
                listener::categoryClicked
            )

            ViewType.BANNERS.ordinal -> BannerViewHolder.create(parent, listener::bannerClicked)
            ViewType.LIVE_ROOM.ordinal -> LiveRoomViewHolder.create(
                parent,
                listener::liveRoomClicked
            )

            ViewType.PK_ROOM.ordinal -> PKRoomViewHolder.create(parent, listener::pkRoomClicked)
            ViewType.EMPTY.ordinal -> object : RecyclerView.ViewHolder(
                View(parent.context)
            ) {}
            else -> throw IllegalArgumentException("unknown viewType: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position)
        when {
            holder is CategoryListViewHolder && item is HomePkItem.CategoryList -> {
                val selectedId = selectedCategory?.id ?: item.categories.firstOrNull()?.id ?: -1
                holder.bind(selectedId, item.categories)
            }

            holder is LiveRoomViewHolder && item is HomePkItem.LiveRoom -> holder.bind(item.room)
            holder is BannerViewHolder && item is HomePkItem.Banners -> holder.bind(item.banners)
            holder is PKRoomViewHolder && item is HomePkItem.PKRoom -> holder.bind(item.room)

        }
        checkPreload(position)

        holder.itemView.alphaAnim()
    }

    // 判断是否进行预加载
    private fun checkPreload(position: Int) {
        if (onPreload != null
            && position == (currentList.size - 1 - preloadItemCount).coerceAtLeast(0)// 索引值等于阈值
//            && scrollState != RecyclerView.SCROLL_STATE_IDLE // 列表正在滚动
            && !isPreloading // 预加载不在进行中
        ) {
            isPreloading = true // 表示正在执行预加载
            onPreload?.invoke()
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is HomePkItem.CategoryList -> ViewType.CATEGORY_LIST.ordinal
            is HomePkItem.Banners -> ViewType.BANNERS.ordinal
            is HomePkItem.LiveRoom -> ViewType.LIVE_ROOM.ordinal
            is HomePkItem.PKRoom -> ViewType.PK_ROOM.ordinal
        }
    }

    override fun getItemCount(): Int = currentList.size

    private fun getItem(position: Int): HomePkItem = currentList[position]

    fun submitList(list: List<HomePkItem>, selectedCategory: CategoryBean) {
        this.selectedCategory = selectedCategory
        submitList(list)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun submitList(list: List<HomePkItem>) {
        currentList.clear()
        currentList.addAll(list)
        notifyDataSetChanged()
    }

    fun addList(list: List<HomePkItem>) {
        val startIndex = currentList.size
        currentList.addAll(list)
        notifyItemRangeInserted(startIndex, list.size)
    }

    fun currentList(): List<HomePkItem> = currentList

    interface ItemClickListener {
        fun categoryClicked(category: CategoryBean)
        fun liveRoomClicked(room: LiveListBean.ListBean)
        fun bannerClicked(banner: NewBannerBean)
        fun pkRoomClicked(room: LiveChannel)
    }
}