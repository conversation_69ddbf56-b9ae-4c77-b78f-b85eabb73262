package com.yunbao.main.dialog

import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import com.yunbao.common.Constants
import com.yunbao.common.EventCode
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.bean.EventBean
import com.yunbao.common.interfaces.CommonCallback
import com.yunbao.common.reactivehttp.base.BaseRectDialogFragment
import com.yunbao.common.utils.UriUtils
import com.yunbao.common.utils.click
import com.yunbao.common.utils.loadImg
import com.yunbao.common.utils.nav2Act
import com.yunbao.common.utils.registerEvent
import com.yunbao.common.utils.screenHeight
import com.yunbao.common.utils.unregisterEvent
import com.yunbao.main.R
import com.yunbao.main.adapter.LiveGuardAdapter
import com.yunbao.main.bean.LiveFirstGuardBean
import com.yunbao.main.bean.LiveGuardBean
import com.yunbao.main.bean.LiveGuardListBean
import com.yunbao.main.databinding.DialogGuardingBinding
import com.yunbao.main.http.MainHttpUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 *
 * @Description ：直播间守护
 */
class LiveGuardingDialogFragment : BaseRectDialogFragment() {
    companion object {
        const val LIVING_HEAD = "living_head"
        const val HOST_ID = "hostId"

        fun newInstance(living_head: String, hostId: Int) = LiveGuardingDialogFragment().apply {
            arguments = Bundle().apply {
                putString(LIVING_HEAD, living_head)
                putInt(HOST_ID, hostId)
            }
        }
    }

    private var pageNo = 1
    private var totalPage = 0
    var hostId = 0
    var living_head = ""
    val list: MutableList<LiveGuardBean> = mutableListOf()
    val adapter: LiveGuardAdapter by lazy {
        LiveGuardAdapter()
    }

    override val bind by getBind<DialogGuardingBinding>()


    override fun initView() {
        registerEvent()
        arguments?.apply {
            living_head = get(LIVING_HEAD).toString()
            hostId = getInt(HOST_ID)
        }
        val height = (screenHeight(requireContext()) * 0.6f).toInt()
        val window = dialog?.window
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        val params = window?.attributes
        params?.width = WindowManager.LayoutParams.MATCH_PARENT
        params?.height = height
        params?.gravity = Gravity.BOTTOM
        params?.dimAmount = 0.0f
        window?.attributes = params
        bind.rvGuardingList.adapter = adapter
        bind.btnGuard.setOnClickListener {
            mGuardingButtonClickListener?.click()
            this.dismiss()
        }
        bind.root.setBackgroundResource(R.drawable.bg_bouns_2)
        bind.ivGuardingTips.setImageResource(R.drawable.skin_icon_guard_introduce_white)
        context?.getColor(R.color.black)?.let { bind.tvGuardingTitle.setTextColor(it) }
        bind.lastWeekGuardText.setTextAppearance(R.style.skin_guard_week_text_white)
        bind.guardDayText.setTextAppearance(R.style.skin_guard_week_text_white)
        bind.columnWeekTop.setTextAppearance(R.style.skin_guard_list_column_white)
        bind.columnNickName.setTextAppearance(R.style.skin_guard_list_column_white)
        bind.columnDay.setTextAppearance(R.style.skin_guard_list_column_white)
        bind.columnGuardValue.setTextAppearance(R.style.skin_guard_list_column_white)
        bind.layoutListColumn.setBackgroundResource(R.drawable.skin_shape_guard_list_title_bg_white)
        bind.tag.setImageResource(R.drawable.skin_icon_sigh)
        bind.noticeText.setTextAppearance(R.style.skin_guard_sigh_white)

        bind.ivGuardingTips.click {
            val bundle = Bundle()
            var url = Constants.H5_GUARDING
            url = UriUtils.addUriParameter(url, "uid", "${LiveApp.userId}")
            url = UriUtils.addUriParameter(url, "token", LiveApp.token)
            bundle.putString(Constants.URL, url)
            nav2Act(RoutePath.PATH_WEBVIEW_DSBRIDGE, bundle, Constants.DEFAULT_FLAG)
        }
        bind.refreshLayout.setOnRefreshListener {
            pageNo = 1
            getGuardingListData()
        }

        bind.refreshLayout.setOnLoadMoreListener {
            ++pageNo
            getGuardingListData()
        }

//        postEvent(EventBean(EventCode.EVENT_DIALOG_HEIGHT_CHANGE, height))
    }

    override fun getDialogStyle() = R.style.dialog

    override fun initData() {
//        bind.ivLiveAvatar.loadPic(
//            living_head,
//            isCircle = true,
//            error = R.drawable.living_head_default
//        )
        getGuardingListData()
    }

    private fun getGuardingListData() {
        MainHttpUtil.getLiveGuardList(
            hostId, pageNo,
            object : CommonCallback<LiveGuardListBean>() {
                override fun callback(beans: LiveGuardListBean) {

//                    val count: Int = beans.list.size
//                    var limit = 15
//                    totalPage = if (count % limit == 0) {
//                        count / limit
//                    } else {
//                        count / limit + 1
//                    }
                    bind.refreshLayout.finishRefresh()
                    bind.refreshLayout.finishLoadMore()
                    if (beans.list.size == 15) {
                        bind.refreshLayout.setEnableLoadMore(true)
                    } else {
                        bind.refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                    if (pageNo == 1) {
                        list.clear()
                        list.addAll(beans.list)
                        adapter.setList(list)
                        setFirstGuardData(beans.first)
                        if (list.size == 0) {
                            adapter.setEmptyView(R.layout.layout_no_data_guard)
                        }
                    } else {
                        adapter.addData(beans.list)
                    }
                }
            })
    }

    private fun setFirstGuardData(liveFirstGuardBean: LiveFirstGuardBean?) {
        if (liveFirstGuardBean == null && bind.llFirst.visibility == View.GONE) {
            return
        }
        context?.getColor(R.color.black)?.let { bind.tvName.setTextColor(it) }
        bind.llFirst.visibility = View.VISIBLE
        bind.avatar.loadImg(liveFirstGuardBean?.avatar, R.drawable.ph_avatar)
        bind.tvName.text = liveFirstGuardBean?.nickname
        if (!TextUtils.isEmpty(liveFirstGuardBean?.fire)) {
            bind.lastWeekGuardValue.text = "${liveFirstGuardBean?.fire}"
        } else {
            bind.lastWeekGuardValue.text = "0"
        }
        bind.guardDayValue.text = "${liveFirstGuardBean?.day}"
        when (liveFirstGuardBean?.type) { //周守护 type=1  月守护 type=2，年守护 type=3
            1 -> {
                bind.avatarBg.setBackgroundResource(R.drawable.skin_shape_guard_avatar_bg_week)
                bind.ivGuardType.setImageResource(R.drawable.skin_icon_guard_week_white)
            }

            2 -> {
                bind.avatarBg.setBackgroundResource(R.drawable.skin_shape_guard_avatar_bg_month)
                bind.ivGuardType.setImageResource(R.drawable.skin_icon_guard_month_white)
            }

            3 -> {
                bind.avatarBg.setBackgroundResource(R.drawable.skin_shape_guard_avatar_bg_year)
                bind.ivGuardType.setImageResource(R.drawable.skin_icon_guard_year_white)
            }
        }

    }

    private fun sortList() {
        var position = -1
        for ((index, value) in list.withIndex()) {
            if (value.id == LiveApp.userId) {
                position = index
                break
            }
        }
        if (position != -1) {
            val temp = list[position]
            list.removeAt(position)
            list.add(0, temp)
        }
        if (list.isEmpty()) {
            val liveGuardBean = LiveGuardBean()
            liveGuardBean.isEmpty = true
            list.add(0, liveGuardBean)
        }
    }

    var mGuardingButtonClickListener: GuardingButtonClickListener? = null

    fun setGuardingButtonClickListener(guardingButtonClickListener: GuardingButtonClickListener) {
        mGuardingButtonClickListener = guardingButtonClickListener
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
//        postEvent(EventBean(EventCode.EVENT_DIALOG_HEIGHT_CHANGE, 0))
        unregisterEvent()
    }

    interface GuardingButtonClickListener {
        fun click()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun event(event: EventBean) {
        when (event.eventCode) {
            EventCode.EVENT_LIVE_FINISH -> {
                dismissAllowingStateLoss()
            }
        }
    }
}