package com.yunbao.main.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.recyclerview.widget.GridLayoutManager
import com.yunbao.common.Constants
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.interfaces.CommonCallback
import com.yunbao.common.reactivehttp.base.BaseRectDialogFragment
import com.yunbao.common.utils.*
import com.yunbao.main.R
import com.yunbao.main.adapter.LiveGuardBuyAdapter
import com.yunbao.main.adapter.LiveGuardBuyRightAdapter
import com.yunbao.main.bean.BalanceBean
import com.yunbao.main.bean.LiveGuardBuyBean
import com.yunbao.main.databinding.DialogGuardingBuyBinding
import com.yunbao.main.http.MainHttpUtil
import java.util.*

/**
 *
 * @Description ：直播间守护购买界面
 */
class LiveGuardingBuyDialogFragment : BaseRectDialogFragment() {
    companion object {
        const val HOST_ID = "hostId"

        fun newInstance(hostId: Int) = LiveGuardingBuyDialogFragment().apply {
            arguments = Bundle().apply {
                putInt(HOST_ID, hostId)
            }
        }
    }

    var hostId = 0
    var roomId = 0
    val list: MutableList<LiveGuardBuyBean> = mutableListOf()
    val adapter: LiveGuardBuyAdapter by lazy {
        LiveGuardBuyAdapter()
    }
    val rightList: MutableList<LiveGuardBuyBean.PrivilegesBean> = mutableListOf()
    val rightAdapter: LiveGuardBuyRightAdapter by lazy {
        LiveGuardBuyRightAdapter()
    }
    override val bind by getBind<DialogGuardingBuyBinding>()
    var curItem: LiveGuardBuyBean? = null
    private var curDiamonds = 0

    @SuppressLint("NotifyDataSetChanged")
    override fun initView() {
        arguments?.apply {
            hostId = getInt(HOST_ID)
            roomId = getInt(Constants.ROOM_ID)
        }
        val window = dialog?.window
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        val params = window?.attributes
        params?.width = WindowManager.LayoutParams.MATCH_PARENT
        params?.height = WindowManager.LayoutParams.WRAP_CONTENT
        params?.gravity = Gravity.BOTTOM
        params?.dimAmount = 0.0f
        window?.attributes = params
        bind.rvGuardingBuyList.layoutManager = GridLayoutManager(context, 3)
        bind.rvGuardingBuyList.adapter = adapter
        adapter.setOnItemClickListener { adapter, view, position ->
            list.forEach {
                it.isSelect = false
            }
            curItem = list[position]
            list[position].isSelect = true
            adapter.notifyDataSetChanged()
            showTipView(list[position])
        }
        bind.rvGuardingBugRightList.layoutManager = GridLayoutManager(context, 4)
        bind.rvGuardingBugRightList.adapter = rightAdapter
        bind.btnGuard.setOnClickListener {
            btnGuardClick()
        }
        bind.ivGuardingTips.click {
            val bundle = Bundle()
            var url = Constants.H5_GUARDING
            url = UriUtils.addUriParameter(url, "uid", "${LiveApp.userId}")
            url = UriUtils.addUriParameter(
                url, "token", LiveApp.token
            )
            bundle.putString(Constants.URL, url)
            nav2Act(RoutePath.PATH_WEBVIEW_DSBRIDGE, bundle, Constants.DEFAULT_FLAG)
        }
        bind.root.setBackgroundResource(R.drawable.bg_bouns_2)
        bind.tvGuardingTag.setTextAppearance(R.style.skin_guard_tag_text_white)
        bind.ivGuardingTips.setImageResource(R.drawable.skin_icon_guard_introduce_white)
        bind.layoutBlance.setBackgroundResource(R.drawable.skin_shape_guard_bg_balance_white)
        bind.diamondIcon.setImageResource(R.drawable.skin_icon_diamond_white)
        bind.btnGuard.setBackgroundResource(R.drawable.skin_bg_btn_guard_white)
        bind.tvDiamondsNum.setTextAppearance(R.style.skin_guard_diamond_count_white)
        bind.tvBalanceNum.setTextAppearance(R.style.skin_guard_balance_white)
    }

    private fun btnGuardClick() {
        curItem?.let {
//            if (it.price > curDiamonds.toDouble()) {
//                //余额不足
//                DialogUitl.showSimpleDialog(
//                    context, "钱包余额不足，是否前往充值？","去充值"
//                ) { dialog, content ->
//                    RechargeActivity.forward(context,false)
//                }
//            } else {
            @SuppressLint("DefaultLocale") val captcha =
                String.format("%04d", Random().nextInt(9999))
            DialogUitl.showConfirmOrderDialog(
                context,
                "确认守护",
                "需要消耗" + it.price + "钻石",
                captcha
            ) { dialog: Dialog, content: String ->
                if (captcha == content) {
                    addGuardingOrder()
                    dialog.dismiss()
                } else {
                    showCustomToastFailed("验证码错误")
                }
            }
//            }
        }
    }

    private fun addGuardingOrder() {
        curItem?.let {
            MainHttpUtil.guardingBuy(
                hostId,
                it.id,
                roomId,
                object : CommonCallback<Boolean>() {
                    override fun callback(flag: Boolean) {
                        addGuardingOrderListener?.success()
                        showCustomToastSuccess("开通成功")
                        dismiss()
                    }
                })
        }

    }

    override fun getDialogStyle() = R.style.dialog

    override fun initData() {
        getGuardingBuyData()
        getUserMoney()
    }

    private var addGuardingOrderListener: AddGuardingOrderListener? = null

    fun setAddGuardingListener(listener: AddGuardingOrderListener) {
        addGuardingOrderListener = listener
    }

    interface AddGuardingOrderListener {
        fun success()
    }

    private fun getUserMoney() {
        MainHttpUtil.getUserMoney(object : CommonCallback<BalanceBean>() {
            @SuppressLint("SetTextI18n")
            override fun callback(balanceBean: BalanceBean) {
                curDiamonds = balanceBean.diamonds
                bind.tvBalanceNum.text = balanceBean.points?.toString() + ""
                bind.tvDiamondsNum.text = balanceBean.diamonds?.toString() + ""
            }
        })
    }

    private fun getGuardingBuyData() {
        MainHttpUtil.getGuardingBuyData(
            hostId,
            object : CommonCallback<List<LiveGuardBuyBean>>() {
                override fun callback(beans: List<LiveGuardBuyBean>) {
                    list.clear()
                    list.addAll(beans)
                    adapter.setNewInstance(list)
                    if (list.size > 0) {
                        curItem = list[0]
                        list[0].isSelect = true
                        showTipView(list[0])
                    }
                }
            })
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun showTipView(item: LiveGuardBuyBean) {
        rightList.clear()
        if (null != item.privileges) {
            rightList.addAll(item.privileges)
        }
        rightAdapter.setNewInstance(rightList)
        rightAdapter.notifyDataSetChanged()
    }
}