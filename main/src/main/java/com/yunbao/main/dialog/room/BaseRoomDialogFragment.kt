package com.yunbao.main.dialog.room

import android.content.DialogInterface
import android.os.Bundle
import android.view.ViewTreeObserver
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ktx.immersionBar
import com.yunbao.common.Constants
import com.yunbao.common.EventCode
import com.yunbao.common.bean.EventBean
import com.yunbao.common.reactivehttp.base.BaseRectDialogFragment
import com.yunbao.common.utils.click
import com.yunbao.common.utils.dp
import com.yunbao.common.utils.invisible
import com.yunbao.common.utils.log
import com.yunbao.common.utils.postEvent
import com.yunbao.common.utils.registerEvent
import com.yunbao.common.utils.showValueAnimator
import com.yunbao.common.utils.unregisterEvent
import com.yunbao.common.utils.visibility
import com.yunbao.game.fragment.RoomGameLotteryDataDialogFragment
import com.yunbao.game.util.tryCatch
import com.yunbao.main.R
import com.yunbao.main.databinding.FragmentBaseRoomDialogBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.max

/**
 * 直播间活动、游戏、礼物弹框
 */
class BaseRoomDialogFragment : BaseRectDialogFragment() {
    private var type = Constants.TYPE_DIALOG_GAME_ENTRANCE
    private var curFragment: Fragment? = null
    private var curIndex = 0

    //今日投注、开奖历史是否展示
    private var isTodayBetShow = false
    private var isLotteryHistoryShow = false
    private var defaultHeight = 340.dp
    private lateinit var globalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener
    private val fragmentMap = HashMap<Int, Fragment>()

    override val bind by getBind<FragmentBaseRoomDialogBinding>()

    override fun initView() {
        dialog?.window?.setWindowAnimations(R.style.bottomToTopAnim)
        registerEvent()
        arguments?.let {
            type = it.getInt(Constants.TYPE)
        }
        showWithType()
        tryCatch {
            requireActivity().supportFragmentManager.setFragmentResultListener(
                Constants.BASE_ROOM_FRAGMENT_RESULT,
                this
            ) { _, bundle ->
                arguments?.apply {
                    type = bundle.getInt(Constants.TYPE)
                    val gameId = bundle.getInt(Constants.GAME_ID)
                    putInt(Constants.TYPE, type)
                    putInt(Constants.GAME_ID, gameId)
                    showWithType()
                }
            }
            defaultHeight = resources.getDimensionPixelOffset(R.dimen.room_dialog_height)
            requireActivity().supportFragmentManager.setFragmentResultListener(
                Constants.BASE_ROOM_HEIGHT,
                this
            ) { _, bundle ->
                val keyboardHeight = bundle.getInt(Constants.BASE_ROOM_HEIGHT)
                val needAnim = bundle.getBoolean(Constants.NEED_ANIM, false)
                if (keyboardHeight == 0) {
                    //键盘收起
                    showWithType()
                }
                val h = max(keyboardHeight, defaultHeight)
                val viewH = bind.flContainer.height
                if (!needAnim) {
                    bind.flContainer.updateLayoutParams {
                        height = h
                    }
                } else {
                    bind.flContainer.showValueAnimator(viewH, h)
                }
            }
            globalLayoutListener = OnGlobalLayoutListener {
                postEvent(EventBean(EventCode.EVENT_DIALOG_HEIGHT_CHANGE, bind.root.height - bind.vClick.height))
            }
            bind.root.viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)
        }
    }

    override fun clickEvent() {
        bind.apply {
            ivClose.click {
                closeKeyboard()
                dismissAllowingStateLoss()
            }
            ivMsg.click {
                showSendMessage()
                gameOptionVisible(false)
            }
            ivGift.click {
                showGift()
                gameOptionVisible(false)
            }
            ivGame.click {
                isLotteryHistoryShow = false
                isTodayBetShow = false
                showGameEntrance()
            }
            ivActivity.click {
                showActivity()
                gameOptionVisible(false)
            }
            ivShare.click {
                closeKeyboard()
                postEvent(EventBean(EventCode.EVENT_OPEN_SHARED_PAGE))
                dismissAllowingStateLoss()
            }
            ivGameIns.click {
                if (!it.isVisible) return@click
                if (curIndex == Constants.TYPE_DIALOG_GAME_INSTRUCTIONS) {
                    if ((arguments?.getInt(Constants.GAME_ID) ?: 0) > 0) {
                        arguments?.putInt(Constants.TYPE, Constants.TYPE_DIALOG_GAME_LOTTERY)
                        showGameLottery()
                    } else {
                        arguments?.putInt(Constants.TYPE, Constants.TYPE_DIALOG_GAME_ENTRANCE)
                        showGameEntrance()
                    }
                } else {
                    showGameInstructions()
                }
            }
            ivTodayBet.click {
                if (!it.isVisible) return@click
                log(">>>>>>>>>>>curIndex = $curIndex, isTodayBetShow = $isTodayBetShow")
                isTodayBetShow = if (curIndex == Constants.TYPE_DIALOG_TODAY_BET) {
                    !isTodayBetShow
                } else {
                    true
                }
                isLotteryHistoryShow = false
                showTodayBet()
            }
            ivLotteryHistory.click {
                if (!it.isVisible) return@click
                log(">>>>>>>>>>>curIndex = $curIndex, isLotteryHistoryShow = $isLotteryHistoryShow")
                isLotteryHistoryShow = if (curIndex == Constants.TYPE_DIALOG_LOTTERY_HISTORY) {
                    !isLotteryHistoryShow
                } else {
                    true
                }
                isTodayBetShow = false
                showLotteryHistory()
            }
            vClick.click {
                closeKeyboard()
                dismissAllowingStateLoss()
            }
        }
    }

    private fun showWithType() {
        type = arguments?.getInt(Constants.TYPE) ?: Constants.TYPE_DIALOG_GAME_ENTRANCE
        when (type) {
            Constants.TYPE_DIALOG_GAME_ENTRANCE -> {
                showGameEntrance()
            }

            Constants.TYPE_DIALOG_ACTIVITY -> {
                showActivity()
            }

            Constants.TYPE_DIALOG_GIFT -> {
                showGift()
            }

            Constants.TYPE_DIALOG_GAME_INSTRUCTIONS -> {
                showGameInstructions()
            }

            Constants.TYPE_DIALOG_GAME_LOTTERY -> {
                showGameLottery()
            }

            Constants.TYPE_DIALOG_TODAY_BET -> {
                showTodayBet()
            }

            Constants.TYPE_DIALOG_LOTTERY_HISTORY -> {
                showLotteryHistory()
            }
        }
    }

    /**
     * 展示游戏入口
     */
    private fun showGameEntrance() {
        type = arguments?.getInt(Constants.TYPE) ?: Constants.TYPE_DIALOG_GAME_ENTRANCE
        if (type == Constants.TYPE_DIALOG_GAME_LOTTERY || (arguments?.getInt(Constants.GAME_ID) ?: 0) > 0) {
            showGameLottery()
        } else {
            if (curIndex == Constants.TYPE_DIALOG_GAME_ENTRANCE) return
            resetContainerHeight()
            gameOptionVisible(false)
            resetSelectedState()
            bind.ivGame.isSelected = true
            curIndex = Constants.TYPE_DIALOG_GAME_ENTRANCE
            arguments?.let { bundle ->
                bundle.putInt(Constants.TYPE, curIndex)
                val f = fragmentMap[curIndex]
                val fragment = f ?: RoomGameEntranceDialogFragment.newInstance(bundle)
                val ft = childFragmentManager.beginTransaction()
                if (f == null) {
                    fragmentMap[curIndex] = fragment
                    ft.add(R.id.fl_container, fragment, "tag_$curIndex")
                }
                ft.show(fragment)
                curFragment?.let {
                    ft.hide(it)
                }
                curFragment = fragment
                ft.commitAllowingStateLoss()
            }
        }
    }

    /**
     * 展示输入框
     */
    private fun showSendMessage() {
        if (curIndex == Constants.TYPE_DIALOG_SEND_MESSAGE) return
        gameOptionVisible(false)
        resetSelectedState()
        bind.ivMsg.isSelected = true
        curIndex = Constants.TYPE_DIALOG_SEND_MESSAGE
        arguments?.let { bundle ->
            val f = fragmentMap[curIndex]
            val fragment = f ?: RoomSendMessageFragment.newInstance(bundle)
            val ft = childFragmentManager.beginTransaction()
            if (f == null) {
                fragmentMap[curIndex] = fragment
                ft.add(R.id.fl_container, fragment, "tag_$curIndex")
            }
            ft.show(fragment)
            curFragment?.let {
                ft.hide(it)
            }
            curFragment = fragment
            ft.commitAllowingStateLoss()
        }
    }

    /**
     * 展示具体游戏
     */
    private fun showGameLottery(needResetType: Boolean = true) {
        log(">>>>>>>>showGameLottery needResetType = $needResetType, curIndex = $curIndex")
        //从今日投注、历史开奖进入
        if (!needResetType && curIndex in arrayOf(
                Constants.TYPE_DIALOG_GAME_LOTTERY,
                Constants.TYPE_DIALOG_TODAY_BET,
                Constants.TYPE_DIALOG_LOTTERY_HISTORY
            )
        ) return

        if (needResetType && curIndex in arrayOf(Constants.TYPE_DIALOG_TODAY_BET, Constants.TYPE_DIALOG_LOTTERY_HISTORY)) {
            //点击进入，但原来在今日投注、历史开奖
            resetSelectedState()
            bind.ivGame.isSelected = true
            curIndex = Constants.TYPE_DIALOG_GAME_LOTTERY
            (curFragment as? RoomGameLotteryDataDialogFragment)?.let {
                it.showTodayBet(false)
                it.showLotteryHistory(false)
            }
            return
        }
        //点击进入，避免重复打开
        if (needResetType && curIndex == Constants.TYPE_DIALOG_GAME_LOTTERY) return
        resetContainerHeight()
        gameOptionVisible(true)
        resetSelectedState()
        bind.ivGame.isSelected = true
        if (needResetType) {
            curIndex = Constants.TYPE_DIALOG_GAME_LOTTERY
        }
        arguments?.let { bundle ->
            if (needResetType) {
                bundle.putInt(Constants.TYPE, Constants.TYPE_DIALOG_GAME_LOTTERY)
            }
            val f = fragmentMap[Constants.TYPE_DIALOG_GAME_LOTTERY]
            val fragment = f ?: RoomGameLotteryDataDialogFragment.newInstance(bundle)
            val ft = childFragmentManager.beginTransaction()
            if (f == null) {
                fragmentMap[curIndex] = fragment
                ft.add(R.id.fl_container, fragment, "tag_$curIndex")
            }
            val gameId = bundle.getInt(Constants.GAME_ID)
            ft.show(fragment)
            curFragment?.let {
                ft.hide(it)
            }
            curFragment = fragment
            ft.commitAllowingStateLoss()

            viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                tryCatch {
                    (fragment as RoomGameLotteryDataDialogFragment).apply {
                        arguments = bundle
                        changeGame(gameId)
                    }
                }
            }
        }
    }

    /**
     * 展示活动
     */
    private fun showActivity() {
        if (curIndex == Constants.TYPE_DIALOG_ACTIVITY) return
        resetContainerHeight()
        resetSelectedState()
        bind.ivActivity.isSelected = true
        arguments?.let { bundle ->
            bundle.putInt(Constants.TYPE, Constants.TYPE_DIALOG_ACTIVITY)
        }
        curIndex = Constants.TYPE_DIALOG_ACTIVITY
        val f = fragmentMap[curIndex]
        val fragment = f ?: RoomActivityDialogFragment()
        val ft = childFragmentManager.beginTransaction()
        if (f == null) {
            fragmentMap[curIndex] = fragment
            ft.add(R.id.fl_container, fragment, "tag_$curIndex")
        }
        ft.show(fragment)
        curFragment?.let {
            ft.hide(it)
        }
        curFragment = fragment
        ft.commitAllowingStateLoss()
    }

    /**
     * 展示礼物
     */
    private fun showGift() {
        if (curIndex == Constants.TYPE_DIALOG_GIFT) return
        resetContainerHeight()
        resetSelectedState()
        bind.ivGift.isSelected = true
        arguments?.let { bundle ->
            bundle.putInt(Constants.TYPE, Constants.TYPE_DIALOG_GIFT)
            curIndex = Constants.TYPE_DIALOG_GIFT
            val f = fragmentMap[curIndex]
            val fragment = f ?: RoomGiftDialogFragment.getInstance(bundle)
            val ft = childFragmentManager.beginTransaction()
            if (f == null) {
                fragmentMap[curIndex] = fragment
                ft.add(R.id.fl_container, fragment, "tag_$curIndex")
            }
            ft.show(fragment)
            curFragment?.let {
                ft.hide(it)
            }
            curFragment = fragment
            ft.commitAllowingStateLoss()
        }
    }

    /**
     * 展示游戏说明
     */
    private fun showGameInstructions() {
        if (curIndex == Constants.TYPE_DIALOG_GAME_INSTRUCTIONS) return
        gameOptionVisible(true)
        resetContainerHeight()
        resetSelectedState()
        bind.ivGameIns.isSelected = true
        arguments?.let { bundle ->
            bundle.putInt(Constants.TYPE, Constants.TYPE_DIALOG_GAME_INSTRUCTIONS)
            curIndex = Constants.TYPE_DIALOG_GAME_INSTRUCTIONS
            val f = fragmentMap[curIndex]
            val fragment = f ?: RoomGameInstructionsFragment.newInstance(bundle)
            val ft = childFragmentManager.beginTransaction()
            if (f == null) {
                fragmentMap[curIndex] = fragment
                ft.add(R.id.fl_container, fragment, "tag_$curIndex")
            }
            ft.show(fragment)
            curFragment?.let {
                ft.hide(it)
            }
            curFragment = fragment
            ft.commitAllowingStateLoss()
        }
    }

    /**
     * 今日投注
     */
    private fun showTodayBet() {
        showGameLottery(false)
        resetSelectedState()
        arguments?.let { bundle ->
            if (!isTodayBetShow) {
                curIndex = Constants.TYPE_DIALOG_GAME_LOTTERY
                bundle.putInt(Constants.TYPE, Constants.TYPE_DIALOG_GAME_LOTTERY)
                bind.ivGame.isSelected = true
            } else {
                bind.ivTodayBet.isSelected = true
                curIndex = Constants.TYPE_DIALOG_TODAY_BET
                bundle.putInt(Constants.TYPE, Constants.TYPE_DIALOG_TODAY_BET)
            }
        }
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            delay(300)
            val fragment = curFragment as? RoomGameLotteryDataDialogFragment
            fragment?.showTodayBet(isTodayBetShow)
        }
    }

    /**
     * 历史开奖
     */
    private fun showLotteryHistory() {
        showGameLottery(false)
        resetSelectedState()
        arguments?.let { bundle ->
            if (!isLotteryHistoryShow) {
                curIndex = Constants.TYPE_DIALOG_GAME_LOTTERY
                bundle.putInt(Constants.TYPE, Constants.TYPE_DIALOG_GAME_LOTTERY)
                bind.ivGame.isSelected = true
            } else {
                bind.ivLotteryHistory.isSelected = true
                curIndex = Constants.TYPE_DIALOG_LOTTERY_HISTORY
                bundle.putInt(Constants.TYPE, Constants.TYPE_DIALOG_LOTTERY_HISTORY)
            }
        }
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            delay(300)
            val fragment = curFragment as? RoomGameLotteryDataDialogFragment
            fragment?.showLotteryHistory(isLotteryHistoryShow)
        }
    }

    /**
     * 游戏操作项可见性
     * 玩法说明、今日投注、开奖历史
     */
    private fun gameOptionVisible(isVisible: Boolean = true) {
        bind.ivGameIns.invisible(!isVisible)
        bind.ivTodayBet.invisible(!isVisible)
        bind.ivLotteryHistory.invisible(!isVisible)
    }

    /**
     * 重置容器高度
     */
    private fun resetContainerHeight() {
        tryCatch {
            bind.flContainer.post {
                val h = bind.flContainer.height
                log(">>>>>>>>>>resetContainerHeight h = $h, defaultHeight = $defaultHeight")
                if (h != defaultHeight) {
                    bind.flContainer.showValueAnimator(h, defaultHeight)
                }
            }
        }
    }

    /**
     * 重置选中状态
     */
    private fun resetSelectedState() {
        bind.apply {
            ivMsg.isSelected = false
            ivGameIns.isSelected = false
            ivTodayBet.isSelected = false
            ivLotteryHistory.isSelected = false
            ivActivity.isSelected = false
            ivGame.isSelected = false
            ivGift.isSelected = false
        }
    }

    /**
     * 关闭键盘
     */
    private fun closeKeyboard() {
        tryCatch {
            if (curIndex == Constants.TYPE_DIALOG_SEND_MESSAGE) {
                (curFragment as RoomSendMessageFragment).onHiddenChanged(true)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun event(event: EventBean) {
        when (event.eventCode) {
            EventCode.EVENT_CLOSE_GAME_ENTRANCE -> {
                closeKeyboard()
                dismissAllowingStateLoss()
            }

            EventCode.EVENT_LIVE_FINISH -> {
                closeKeyboard()
                dismissAllowingStateLoss()
            }

            EventCode.EVENT_SET_CHIPS_DISMISS -> {
                tryCatch {
                    viewLifecycleOwner.lifecycleScope.launch {
                        delay(300)
                        immersionBar {
                            hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
                        }
                    }
                }
            }

            EventCode.EVENT_CLOSE_FOLLOW_ORDER_DIALOG -> {
                bind.linRoot.visibility(true)
            }

            EventCode.EVENT_OPEN_FOLLOW_ORDER_DIALOG -> {
                bind.linRoot.visibility(false)
            }

            EventCode.EVENT_CHANGE_SEL_STATUS -> {
                bind.ivTodayBet.isSelected = false
                bind.ivLotteryHistory.isSelected = false
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        fragmentMap.clear()
        bind.root.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
        postEvent(EventBean(EventCode.EVENT_DIALOG_HEIGHT_CHANGE, 0))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        unregisterEvent()
    }

    companion object {
        @JvmStatic
        fun newInstance(bundle: Bundle) = BaseRoomDialogFragment().apply {
            arguments = bundle
        }
    }
}