package com.yunbao.main.bean

import java.io.Serializable

/**
 * 快速游戏实体类
 */
data class GameQuickBean(
    val id: Int,
    val imgUrl: String?,
    val imgId: Int,
    val name: String?,
    val jumpUrl: String? = null,
    val clickType:Int
): Serializable

/**
 * 快速充值实体类
 */
data class MoneyQuickBean(
    val id: Int,
    val imgUrl: String?,
    val imgId: Int,
    val name: String?
): Serializable

/**
 * 推荐直播类
 */
data class RecommondLiveBean(
    val uid: String,
    val avatar: String?,
    val imgUrl: String?,
    val imgId: Int,
    val title: String?,
    val city: String?,
    val thumb: String?,
    val stream: String,
    val pull: String,
    val roomType: Int,
    val roomVal: String?,
    val number: Int = 0,
    val cpCode: String?
): Serializable

