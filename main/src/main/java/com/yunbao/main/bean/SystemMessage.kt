package com.yunbao.main.bean

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * 系统消息
 */
data class SystemMessage(
    val title: String,
    @SerializedName("created_at")
    val createdAt: String,
    @SerializedName("updated_at")
    val updatedAt: String,
    //0 未读 1 已读
    val status: Int,
    val content: String,
    val url: String,
    val format_type: Double,
    var checked:Boolean,
    var id:Int
) : Serializable
