package com.yunbao.main.bean;

public class LiveGuardBean {

    /**
     * isnoble : true
     * noble_icon : http://**************:8868/static/assets/img/vip/vip_1.png
     * head_frame : http://**************:8868/static/assets/img/vip/vip_1.png
     * hidden : false
     * id : 2
     * avatar :
     * nickname : 2132327
     * level : 0
     * level_icon :
     * sex : 1
     * sex_text : 男
     */

    private boolean isnoble;
    private String noble_icon;
    private String head_frame;
    private boolean hidden;
    private int id;
    private String avatar;
    private String nickname;
    private int level;
    private String level_icon;
    private int sex;
    private String sex_text;
    private boolean isEmpty;
    private String guard_icon;
    private String fire;

    private int day;
    private int type;

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getGuard_icon() {
        return guard_icon;
    }

    public void setGuard_icon(String guard_icon) {
        this.guard_icon = guard_icon;
    }

    public String getFire() {
        return fire;
    }

    public void setFire(String fire) {
        this.fire = fire;
    }

    public boolean isEmpty() {
        return isEmpty;
    }

    public void setEmpty(boolean empty) {
        isEmpty = empty;
    }

    public boolean isIsnoble() {
        return isnoble;
    }

    public void setIsnoble(boolean isnoble) {
        this.isnoble = isnoble;
    }

    public String getNoble_icon() {
        return noble_icon;
    }

    public void setNoble_icon(String noble_icon) {
        this.noble_icon = noble_icon;
    }

    public String getHead_frame() {
        return head_frame;
    }

    public void setHead_frame(String head_frame) {
        this.head_frame = head_frame;
    }

    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getLevel_icon() {
        return level_icon;
    }

    public void setLevel_icon(String level_icon) {
        this.level_icon = level_icon;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public String getSex_text() {
        return sex_text;
    }

    public void setSex_text(String sex_text) {
        this.sex_text = sex_text;
    }
}
