package com.yunbao.main.adapter

import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.fragment.app.FragmentTransaction

class NormalFragmentAdapter(
    private val fm: FragmentManager,
    private val fragments: List<Fragment>,
    private val titles: List<String>? = null
) : FragmentPagerAdapter(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {
    private var mCurTransaction: FragmentTransaction? = null
    override fun getCount() = fragments.size

    override fun getItem(position: Int) = fragments[position]

    override fun getPageTitle(position: Int): CharSequence? {
        return if (titles != null) titles[position] else super.getPageTitle(position)
    }

    override fun isViewFromObject(view: View, `object`: Any): <PERSON><PERSON>an {
        return (`object` as Fragment).view === view
    }

    // 清除缓存fragment，防止首页不同频道交换位置后错乱
    fun clear(container: ViewGroup) {
        try {
            if (mCurTransaction == null) {
                mCurTransaction = fm.beginTransaction()
            }
            for (i in fragments.indices) {
                try {
                    val itemId = getItemId(i)
                    val name = makeFragmentName(container.id, itemId)
                    val fragment = fm.findFragmentByTag(name)
                    if (fragment != null) { //根据对应的ID，找到fragment，删除
                        mCurTransaction?.remove(fragment)
                    }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }
//            mCurTransaction!!.commitNowAllowingStateLoss()
            mCurTransaction?.commitAllowingStateLoss()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    /**
     * 等同于FragmentPagerAdapter的makeFragmentName方法，
     * 由于父类的该方法是私有的，所以在此重新定义
     * @param viewId
     * @param id
     * @return
     */
    private fun makeFragmentName(viewId: Int, id: Long): String {
        return "android:switcher:$viewId:$id"
    }
}