package com.yunbao.main.adapter.report

import android.os.Bundle
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.yunbao.common.Constants
import com.yunbao.common.RoutePath
import com.yunbao.common.utils.nav2Act
import com.yunbao.main.R

class ReportAddImageAdapter constructor(data: ArrayList<String>) : BaseQuickAdapter<String, BaseViewHolder>(
    R.layout.item_report_add_image, data
) {
    override fun convert(holder: BaseViewHolder, item: String) {
        val imageView = holder.getView<ImageView>(R.id.selectImage)
        Glide.with(context).load(item).error(R.drawable.ph_avatar).placeholder(R.drawable.ph_avatar).into(imageView);

        holder.getView<ImageView>(R.id.deleteImage).setOnClickListener {
            remove(item)
            notifyDataSetChanged()
        }
        holder.getView<ImageView>(R.id.selectImage).setOnClickListener {
            nav2Act(RoutePath.PATH_REPORT_SHOW_BIG_IMAGE, Bundle().apply {
                putString(Constants.REPORT_BIG_IMAGE_URL, item)
            }, Constants.DEFAULT_FLAG)
        }
    }

}