package com.yunbao.main.adapter

import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.yunbao.common.Constants
import com.yunbao.common.RoutePath
import com.yunbao.common.utils.nav2Act
import com.yunbao.common.utils.ternary
import com.yunbao.main.R
import com.yunbao.main.bean.RechargeWithdrawRecord
import java.text.SimpleDateFormat
import java.util.*

/**
 * 充提记录数据适配器
 */
class RechargeWithdrawRecordAdapter constructor(data: MutableList<RechargeWithdrawRecord>) :
    BaseQuickAdapter<RechargeWithdrawRecord, BaseViewHolder>(
        R.layout.item_recharge_withdraw_record, data
    ) {

    override fun convert(holder: BaseViewHolder, item: RechargeWithdrawRecord) {
        holder.setText(R.id.tvAmount, "${String.format("%.2f", item.amount)}${ternary(item.awardAmount.isNullOrBlank(), "", "\n${item.awardAmount}")}")
            .setText(R.id.tvPaidMethod, item.paymentMethod)
            .setText(R.id.tvTime, stampToDate(item.time.toInt()))
        holder.getView<TextView>(R.id.tvPaidMethod).visibility = View.VISIBLE
        holder.getView<TextView>(R.id.tvOperate).visibility = View.VISIBLE
        val applyStateStr = when (item.state) {
            1 -> {
                holder.getView<TextView>(R.id.tvStatus)
                    .setTextColor(context.getColor(R.color.color_1372fc))
                "成功"
            }
            2 -> {
                holder.getView<TextView>(R.id.tvStatus)
                    .setTextColor(context.getColor(R.color.color_ff4817))
                "已退回"
            }
            3 -> {
                holder.getView<TextView>(R.id.tvStatus)
                    .setTextColor(context.getColor(R.color.color_34a853))
                if (item.isRecharge) {
                    "处理中"
                } else {
                    "出款中"
                }
            }
            else -> {
                holder.getView<TextView>(R.id.tvStatus)
                    .setTextColor(context.getColor(R.color.color_666666))
                "未处理"
            }
        }
        holder.setText(R.id.tvStatus, applyStateStr)
        if (item.isRecharge) {
            holder.getView<TextView>(R.id.tvPaidMethod).visibility = View.GONE
            holder.getView<TextView>(R.id.tvOperate).visibility = View.GONE
        }
        holder.getView<TextView>(R.id.tvOperate).setOnClickListener {
            nav2Act(RoutePath.PATH_WITHDRAW_HISTORY_DETAIL, Bundle().apply {
                putString(Constants.ORDER_ID, item.orderId)
            })
        }
    }

    /*
     * 将时间戳转换为时间
     */
    fun stampToDate(time: Int): String? {
        val format = SimpleDateFormat("yyyy-MM-dd HH:mm")
        return format.format(Date(time * 1000L))
    }
}