package com.yunbao.main.adapter

import android.annotation.SuppressLint
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.yunbao.main.R
import com.yunbao.main.bean.LiveGuardBuyBean

/**
 * @Description ：
 */
class LiveGuardBuyAdapter :
    BaseQuickAdapter<LiveGuardBuyBean, BaseViewHolder>(R.layout.item_guard_buy_list) {

    @SuppressLint("NotifyDataSetChanged")
    override fun convert(holder: BaseViewHolder, item: LiveGuardBuyBean) {
        val ll_select = holder.getView<RelativeLayout>(R.id.ll_select)
        val ll_bg = holder.getView<LinearLayout>(R.id.ll_bg)
        val iv_icon = holder.getView<ImageView>(R.id.iv_icon)
        val tv_name = holder.getView<TextView>(R.id.tv_name)
        tv_name.text = item.name
        val tv_num = holder.getView<TextView>(R.id.tv_num)
        tv_num.text = item.price.toString()
        when (holder.adapterPosition) {
            2 -> {
                //年守护
                iv_icon.setImageResource(R.drawable.skin_icon_guard_year_white)
                if (item.isSelect) {
                    ll_select.setBackgroundResource(R.drawable.skin_shape_guard_selected_bounds_white)
                } else {
                    ll_select.background  = null
                }
                ll_bg.setBackgroundResource(R.drawable.skin_bg_guard_year_white)
            }
            1 -> {
                iv_icon.setImageResource(R.drawable.skin_icon_guard_month_white)
                //月守护
                if (item.isSelect) {
                    ll_select.setBackgroundResource(R.drawable.skin_shape_guard_selected_bounds_white)
                } else {
                    ll_select.background  = null
                }
                ll_bg.setBackgroundResource(R.drawable.skin_bg_guard_month_white)
            }
            0 -> {
                iv_icon.setImageResource(R.drawable.skin_icon_guard_week_white)
                //周守护
                if (item.isSelect) {
                    ll_select.setBackgroundResource(R.drawable.skin_shape_guard_selected_bounds_white)
                } else {
                    ll_select.background  = null
                }
                ll_bg.setBackgroundResource(R.drawable.skin_bg_guard_week_white)
            }
        }
        tv_name.setTextAppearance(R.style.skin_guard_type_text_white)
        tv_num.setTextAppearance(R.style.skin_guard_money_text_white)
    }
}