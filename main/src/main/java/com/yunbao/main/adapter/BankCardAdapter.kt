package com.yunbao.main.adapter

import androidx.constraintlayout.widget.ConstraintLayout
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.yunbao.main.R
import com.yunbao.main.bean.BankCard
import com.yunbao.common.utils.dp

/**
 * 银行卡数据列表
 */
class BankCardAdapter constructor(data: MutableList<BankCard>) :
    BaseQuickAdapter<BankCard, BaseViewHolder>(R.layout.item_withdraw_bank_card, data) {
    override fun convert(holder: BaseViewHolder, item: BankCard) {
        val c_view = holder.getView<ConstraintLayout>(R.id.c_view)
        if (data.size == 1) {
            c_view.layoutParams.width = ConstraintLayout.LayoutParams.MATCH_PARENT
        } else {
            c_view.layoutParams.width = 300.dp
        }
        if (item.isPh) {
            holder.setVisible(R.id.tvAdd, true)
                .setVisible(R.id.ivIcon, false)
                .setVisible(R.id.tvBankName, false)
                //.setVisible(R.id.tvBankType, false)
                .setVisible(R.id.tvTailNumber, false)
        } else {
            holder.setVisible(R.id.tvAdd, false)
                .setVisible(R.id.ivIcon, true)
                .setVisible(R.id.tvBankName, true)
                //.setVisible(R.id.tvBankType, true)
                .setVisible(R.id.tvTailNumber, true)
                .setText(R.id.tvBankName, item.bankName)
                .setText(R.id.tvTailNumber, item.cardNumber)
        }
    }
}