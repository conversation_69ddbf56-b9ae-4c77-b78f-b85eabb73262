package com.yunbao.main.fragment

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.yunbao.common.Constants
import com.yunbao.common.RoutePath
import com.yunbao.common.bean.LiveInfoBean
import com.yunbao.common.bean.LiveRoomInfo
import com.yunbao.common.bean.LiveRoomListBeanItem
import com.yunbao.common.bean.LiveRoomListType
import com.yunbao.common.interfaces.CommonCallback
import com.yunbao.common.reactivehttp.base.BaseRectFragment
import com.yunbao.common.utils.ClickHelper
import com.yunbao.common.utils.nav2Act
import com.yunbao.common.utils.pageDataOperate
import com.yunbao.common.utils.saveRoomListData
import com.yunbao.common.utils.saveRoomListDataAsync
import com.yunbao.common.utils.toJson
import com.yunbao.main.adapter.RankingTypePageAdapter
import com.yunbao.main.databinding.FragmentRankingSubPageBinding
import com.yunbao.main.http.MainHttpUtil
import com.yunbao.main.reactivehttp.viewmodel.LiveVM
import com.yunbao.main.utils.TimeUtil
import com.yunbao.main.views.RankTopInfoView
import kotlinx.coroutines.runBlocking

class RankingSubPageFragment : BaseRectFragment() {
    companion object {
        /**
         * @param type 日榜，周榜，月榜
         * @param roleTpe 主播，土豪，PK
         */
        fun newInstance(type: Int, roleTpe: Int) =
            RankingSubPageFragment().apply {
                arguments = Bundle().apply {
                    putInt(Constants.TYPE, type)
                    putInt(Constants.ROLE_TYPE, roleTpe)
                }
            }
    }

    var type = LiveRoomListType.DAY_LIST.value

    private var roleType = 0
    private val pageSize = 20
    private var startDate = ""
    private var endDate = ""

    private lateinit var mAdapter: RankingTypePageAdapter

    private val pattern: String = "yyyy-MM-dd"

    override val bind by getBind<FragmentRankingSubPageBinding>()

    override fun initParams() {
        arguments?.let {
            type = it.getInt(Constants.TYPE)
            roleType = it.getInt(Constants.ROLE_TYPE)
        }
    }

    override fun initView() {
        mAdapter = RankingTypePageAdapter(mutableListOf(), roleType)
        mAdapter.setOnItemChildClickListener { _, _, position ->
            if (ClickHelper.isFastDoubleClick()) return@setOnItemChildClickListener
            val item = mAdapter.data[position]
            go2liveRoom(item.user_info?.id ?: 0)
        }
        when (type) {
            LiveRoomListType.DAY_LIST.value -> {
                startDate = TimeUtil.getToday(pattern)
                endDate = TimeUtil.getToday(pattern)
            }

            LiveRoomListType.WEEK_LIST.value -> {
                startDate = TimeUtil.getWeekStart(pattern)
                endDate = TimeUtil.getWeekEnd(pattern)
            }

            LiveRoomListType.MONTH_LIST.value -> {
                startDate = TimeUtil.getMonthFirstDay(pattern)
                endDate = TimeUtil.getMonthEndDay(pattern)
            }
        }
        val linearLayoutManager = LinearLayoutManager(requireContext())
        bind.mRv.apply {
            layoutManager = linearLayoutManager
            setHasFixedSize(true)
            adapter = mAdapter
        }

        bind.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                vm.value.page.value = Constants.FIRST_PAGE
                vm.value.getRankingPageFull(
                    roleType,
                    pageSize,
                    startDate,
                    endDate
                )
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                vm.value.getRankingPageFull(
                    roleType,
                    pageSize,
                    startDate,
                    endDate
                )
            }
        })
    }

    private val vm by lazy {
        getViewModel<LiveVM> {
            liveRoomListBean.observe(this@RankingSubPageFragment) {
                if (!it.isNullOrEmpty()) {
                    if (page.value == Constants.FIRST_PAGE) {
                        if (it.size > 3) {
                            showTop3(it.take(3))
                            mAdapter.addData(it.subList(3, it.size))
                            pageDataOperate(page, it.subList(3, it.size), bind.refreshLayout, mAdapter, pageSize = pageSize - 3)
                        } else {
                            showTop3(it)
                        }
                    } else {
                        pageDataOperate(page, it, bind.refreshLayout, mAdapter, pageSize = pageSize)
                    }
                } else {
                    if (page.value == Constants.FIRST_PAGE) {
                        mAdapter.setNewData(mutableListOf())
                    } else {
                        bind.refreshLayout.finishRefreshWithNoMoreData()
                    }
                }
            }

            requestFinish.observe(this@RankingSubPageFragment) {
                if (it) {
                    bind.refreshLayout.finishRefresh()
                } else {
                    bind.refreshLayout.finishLoadMore()
                }
            }
        }
    }

    /**
     * 展示前三项
     */
    private fun showTop3(list: List<LiveRoomListBeanItem>) {
        list.forEachIndexed { index, item ->
            when (index) {
                0 -> {
                    bind.top1.setData(item, roleType, true)
                    bind.top1.setCallback(object : RankTopInfoView.RankTopInfoCallback {
                        override fun itemClick(item: LiveRoomListBeanItem) {
                            go2liveRoom(item.user_info?.id ?: 0)
                        }
                    })
                }

                1 -> {
                    bind.top2.setData(item, roleType)
                    bind.top2.setCallback(object : RankTopInfoView.RankTopInfoCallback {
                        override fun itemClick(item: LiveRoomListBeanItem) {
                            go2liveRoom(item.user_info?.id ?: 0)
                        }
                    })
                }

                2 -> {
                    bind.top3.setData(item, roleType)
                    bind.top3.setCallback(object : RankTopInfoView.RankTopInfoCallback {
                        override fun itemClick(item: LiveRoomListBeanItem) {
                            go2liveRoom(item.user_info?.id ?: 0)
                        }
                    })
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (vm.value.liveRoomListBean.value == null) {
            bind.refreshLayout.autoRefresh()
        }
    }

//    fun loadData() {
//        LogUtils.d("总榜 start_date:$startDate  end_date:$endDate")
//        vm.value.getRankingPageFull(
//            roleType,
//            pageSize,
//            20,
//            startDate,
//            endDate
//        )
//    }

    private fun go2liveRoom(id: Int) {
        if (id <= 0) return
        MainHttpUtil.getRoomInfoByAnchorId(
            id,
            object : CommonCallback<LiveInfoBean.RoomInfoBean>() {
                override fun callback(room: LiveInfoBean.RoomInfoBean) {
                    if (room.id > 0) {
                        runBlocking {
                            val item = LiveRoomInfo()
                            item.roomId = room.id
                            item.thumb = room.thumb
                            val list = mutableListOf<LiveRoomInfo>()
                            list.add(item)
                            saveRoomListDataAsync(list.toJson())
                            nav2Act(RoutePath.PATH_LIVE_ROOM, Bundle().apply {
                                putInt(Constants.CUR_POSITION, 0)
                                putInt(Constants.START_ROOM_ID, item.roomId)
                            })
                        }
                    }
                }
            })
    }
}