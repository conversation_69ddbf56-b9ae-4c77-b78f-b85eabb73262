package com.yunbao.main.fragment.main

import android.Manifest
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ktx.immersionBar
import com.yunbao.common.Constants
import com.yunbao.common.EventCode
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.activity.WebViewActivity
import com.yunbao.common.bean.EventBean
import com.yunbao.common.bean.GridItemBean
import com.yunbao.common.bean.ImageInfoBean
import com.yunbao.common.interfaces.CommonCallback
import com.yunbao.common.popup.showUtils.CommonPopupShowUtils
import com.yunbao.common.reactivehttp.base.BaseRectFragment
import com.yunbao.common.reactivehttp.http.HttpConfig
import com.yunbao.common.utils.ClickHelper
import com.yunbao.common.utils.DataUtil
import com.yunbao.common.utils.PictureUtil
import com.yunbao.common.utils.WordUtil
import com.yunbao.common.utils.click
import com.yunbao.common.utils.getFromCache
import com.yunbao.common.utils.go2promotion
import com.yunbao.common.utils.loadImg
import com.yunbao.common.utils.loadLocalImg
import com.yunbao.common.utils.nav2Act
import com.yunbao.common.utils.postEvent
import com.yunbao.common.utils.registerEvent
import com.yunbao.common.utils.showCustomToastFailed
import com.yunbao.common.utils.showDialogFragment
import com.yunbao.common.utils.showRichText2
import com.yunbao.common.utils.talkingClickEvent
import com.yunbao.common.utils.ternary
import com.yunbao.common.utils.textCopyThenPost
import com.yunbao.common.utils.unregisterEvent
import com.yunbao.common.utils.visibility
import com.yunbao.game.util.tryCatch
import com.yunbao.main.R
import com.yunbao.main.activity.MyBackPackActivity
import com.yunbao.main.adapter.GridMineAdapter
import com.yunbao.main.databinding.FragmentTabMineBinding
import com.yunbao.main.dialog.CustomerServiceDialogFragment
import com.yunbao.main.http.MainHttpUtil
import com.yunbao.main.reactivehttp.viewmodel.TabMineVM
import com.yunbao.main.utils.isFragmentNotAlive
import com.yunbao.main.utils.loadSVGAImageFromAssets
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class TabMineFragment : BaseRectFragment() {

    private var gridList = mutableListOf<GridItemBean>()

    override val bind by getBind<FragmentTabMineBinding>()

    override fun initView() {
        registerEvent()
        bind.refreshLayout.setOnRefreshListener {
            refreshData(true)
        }
        showMineSettings()

        lifecycleScope.launch {
            LiveApp.userInfoFlow().collectLatest {
                showUserInfo()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (LiveApp.token.isNotBlank()) {
            resetStatusBar()
            refreshData()
        } else {
            postEvent(EventBean(EventCode.EVENT_NEED_LOGIN))
        }
    }

    private fun resetStatusBar() {
        immersionBar {
            titleBar(bind.linTop)
            transparentStatusBar()
            hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            statusBarDarkFont(true)
        }
    }

    private fun refreshData(isRefresh: Boolean = false) {
        if (isFragmentNotAlive()) return
        vm.value.apply {
            getHelpCenter(isRefresh)
            getViMoney(isRefresh)
            vm.value.getUnreadMsg()
        }
        if(isRefresh) {
            postEvent(EventBean(EventCode.EVENT_USER_INFO_CHANGE))
        }
    }

    private val vm by lazy {
        getViewModel<TabMineVM> {
            helpCenterRoute.observe(this@TabMineFragment) {
                if (it.isNullOrBlank()) {
                    gridList.removeIf { item -> item.title == "帮助中心" }
                } else {
                    if (gridList.size != DataUtil.getMineOptionItems().size) {
                        getGridData()
                    }
                }
                val adapter = bind.gridMine.adapter as? GridMineAdapter
                adapter?.notifyDataSetChanged()
            }

            viInfo.observe(this@TabMineFragment) {
                showViInfo()
            }

            unreadMsg.observe(this@TabMineFragment) {
                showUnreadMsg()
            }

            isUpload.observe(this@TabMineFragment) {
                postEvent(EventBean(EventCode.EVENT_USER_INFO_CHANGE))
            }

            requestFinish.observe(this@TabMineFragment) {
                bind.refreshLayout.finishRefresh()
            }
        }
    }

    private fun showUserInfo() {
        val info = LiveApp.getUserInfo()
        if (info != null) {
            bind.apply {
                ivAvatar.loadImg(info.avatar, R.drawable.ph_avatar)
                if (info.knighthood.isBlank()) {
                    ivFrame.visibility = View.INVISIBLE
                } else {
                    ivFrame.visibility(true)
                    requireContext().loadSVGAImageFromAssets(assetName = info.knighthood,
                        onParseCompletion = { videoEntity ->
                            bind.ivFrame.apply {
                                setVideoItem(videoEntity)
                                stepToFrame(0, true)
                            }
                        })
                }
                if (info.levelIcon.isNullOrBlank()) {
                    ivLevel.visibility(false)
                } else {
                    ivLevel.visibility(true)
                    ivLevel.loadImg(info.levelIcon)
                }
                if (info.nobleIcon.isNullOrBlank()) {
                    ivNoble.visibility(false)
                } else {
                    ivNoble.visibility(true)
                    ivNoble.loadImg(info.nobleIcon)
                }
                tvName.text = info.nickname
                ivSex.setImageResource(
                    ternary(
                        info.gender == 2,
                        R.mipmap.sex_woman,
                        R.mipmap.sex_man
                    )
                )
                val id = ternary(info.niceNum.isNullOrBlank(), "${info.id}", info.niceNum)
                tvId.text = WordUtil.getString(R.string.member_id, id)
                tvFollowAmount.text = "${info.followCount}"
            }
        } else if (LiveApp.isLoginResult()) {
            resetUserInfo()
        }
    }

    /**
     * 重置用户信息展示
     */
    private fun resetUserInfo() {
        bind.apply {
            ivAvatar.loadLocalImg(R.drawable.ph_avatar)
            ivFrame.visibility = View.INVISIBLE
            ivLevel.visibility(false)
            ivNoble.visibility(false)
            ivNoble.visibility(false)
            tvName.text = ""
            tvId.text = ""
            tvFollowAmount.text = ""
            bind.tvWalletAmount.text = "0"
        }
    }

    private fun showViInfo() {
        val viInfo = vm.value.viInfo.value
        if (viInfo != null) {
            bind.tvWalletAmount.text = "${viInfo.balance}"
            val tradingList = viInfo.tradingList
            if (!tradingList.isNullOrEmpty()) {
                val item = tradingList[0]
                //bind.tvLeaveTime.text = item.expiredTime
                //bind.txtLeaveTime.visibility(true)
                CommonPopupShowUtils.showAmountNotEnoughPopup(
                    requireContext(),
                    WordUtil.getString(R.string.vi_alert_title),
                    item.tradingStateMsg,
                    WordUtil.getString(R.string.go2handler),
                    WordUtil.getString(R.string.neglect),
                    clickSureListener = {
//                        nav2Act(RoutePath.PATH_WEBVIEW_DSBRIDGE, Bundle().apply {
//                            putString(
//                                Constants.URL,
//                                "${HttpConfig.VI_URL}trade/order?state=${item.tradingState}"
//                            )
//                            putBoolean(Constants.NEED_WX_CLOSE, true)
//                        })
                        nav2Act(RoutePath.PATH_VI_TRADING_FLOOR, Bundle().apply {
                            putInt(Constants.INDEX, 2)  //跳转到订单tab
                        })
                    },
                    clickCancelListener = {

                    },
                    false
                )
            } else {
                //bind.tvLeaveTime.text = ""
                //bind.txtLeaveTime.visibility(false)
            }
        } else {
            bind.tvWalletAmount.text = "0"
        }
    }

    private fun showUnreadMsg() {
        vm.value.unreadMsg.value?.let {
            if (it.noReadNum > 0) {
                bind.tvSystemMsgNum.apply {
                    visibility(true)
                    text = "${it.noReadNum}"
                }
                it.newest?.let { newest ->
                    bind.tvMessage.showRichText2(newest.title)
                }
            } else {
                bind.tvSystemMsgNum.visibility(false)
                bind.tvMessage.setText(R.string.no_message)
            }
            tryCatch {
                var url = getFromCache(Constants.LIVE_INVOKE_URL, "")
                if (url.isNotBlank()) {
                    if (!url.startsWith("http")) {
                        url = "http://$url"
                    }
                    val uri = Uri.parse(url)
                    bind.unreadLiveLink.text = "回家地址: ${uri.host}"
                }
            }
        }
    }

    private fun showMineSettings() {
        getGridData()
        bind.gridMine.adapter =
            GridMineAdapter(requireContext(), gridList as ArrayList<GridItemBean>?)
        bind.gridMine.setOnItemClickListener { _, _, position, _ ->
            if (ClickHelper.isFastDoubleClick()) return@setOnItemClickListener
            val item = gridList[position]
            talkingClickEvent("我的${item.title}")
            when (item.id) {
                1 -> {
                    go2Vip()
                }

                2 -> {
                    nav2Act(RoutePath.PATH_CONSUMPTION_DETAIL)
                }

                3 -> {
                    nav2Act(RoutePath.PATH_PHONE_AUTH)
                }

                4 -> {
                    showDialogFragment(CustomerServiceDialogFragment.newInstance(1))
                }

                5 -> {
                    nav2Act(RoutePath.PATH_GAME_RECORD)
                }

                6 -> {
                    nav2Act(RoutePath.PATH_WITHDRAW_WAY)
                }

                7 -> {
                    nav2Act(RoutePath.PATH_GUARD)
                }

                8 -> {
                    nav2Act(RoutePath.PATH_WEBVIEW_DSBRIDGE, Bundle().apply {
                        putString(Constants.URL, "${HttpConfig.H5_URL}h5/feedback")
                    })
                }

                9 -> {
                    LiveApp.getUserInfo()?.let { user ->
                        MyBackPackActivity.forward(requireActivity(), user.level)
                    }
                }

                10 -> {
                    nav2Act(RoutePath.PATH_SETTING)
                }

                11 -> {
                    nav2Act(RoutePath.PATH_WEBVIEW_DSBRIDGE, Bundle().apply {
                        putString(Constants.URL, vm.value.helpCenterRoute.value ?: "")
                    })
                }
            }
        }
    }

    private fun getGridData() {
        gridList.clear()
        gridList.addAll(DataUtil.getMineOptionItems())
    }

    override fun clickEvent() {
        bind.apply {
            vClickInfoEdit.click {
                nav2Act(RoutePath.PATH_PERSONAL_INFO)
            }
            ivEdit.click {
                nav2Act(RoutePath.PATH_PERSONAL_INFO)
            }
//            relativeMyWallet.click {
//                nav2Act(RoutePath.PATH_WALLET)
//            }
            dtvWallet.click {
                talkingClickEvent("我的钱包")
                nav2Act(RoutePath.PATH_WALLET)
            }

            dtvWithdraw.click {
                talkingClickEvent("我的提现")
                if (LiveApp.isVisit) {
                    context?.let { it1 ->
                        CommonPopupShowUtils.showAmountNotEnoughPopup(
                            it1,
                            "温馨提示",
                            "游客暂不开放，是否去进行手机认证",
                            "确定",
                            clickSureListener = {
                                nav2Act(
                                    RoutePath.PATH_PHONE_AUTH
                                )
                            }
                        )
                    }
                } else {
                    nav2Act(RoutePath.PATH_WITHDRAW_METHOD)
                }
            }
            dtvVip.click {
                talkingClickEvent("我的VIP")
                go2Vip()
            }
            dtvActivity.click {
                talkingClickEvent("我的活动")
                nav2Act(RoutePath.PATH_ACTIVIY)
            }
            dtvPopularize.click {
                talkingClickEvent("我的推广")
                go2promotion()
            }
            //直播地址
            unreadLiveLink.click {
                talkingClickEvent("我的网址")
                var url = getFromCache(Constants.LIVE_INVOKE_URL, "")
                if (url.isNotBlank()) {
                    if (!url.startsWith("http")) {
                        url = "http://$url"
                    }
                    WebViewActivity.forwardBrowser(context, url)
                }
            }
            /**
             * 九宫格点击挪到gridview的item点击
             */
            ivFrame.click {
                modifyAvatar()
            }
            ivAvatar.click {
                modifyAvatar()
            }
            followsLayout.click {
                talkingClickEvent("我的关注主播")
                nav2Act(RoutePath.PATH_FOLLOW)
            }
            viWalletLayout.click(time = 1000) {
                if (LiveApp.isVisit) {
                    context?.let { it1 ->
                        CommonPopupShowUtils.showAmountNotEnoughPopup(
                            it1,
                            "温馨提示",
                            "开通VI钱包，请先绑定手机号",
                            "立即前往",
                            clickSureListener = {
                                nav2Act(
                                    RoutePath.PATH_PHONE_AUTH
                                )
                            }
                        )
                    }
                    return@click
                }
                MainHttpUtil.getViUrl(object : CommonCallback<String>() {
                    override fun callback(redirectUrl: String?) {
                        if (redirectUrl.isNullOrBlank() || !redirectUrl.startsWith("http")) {
                            showCustomToastFailed("Vi币服务维护中，请稍后重试")
                            return
                        }
//                        nav2Act(RoutePath.PATH_WEBVIEW_DSBRIDGE, Bundle().apply {
//                            putString(Constants.URL, HttpConfig.VI_URL)
//                            putBoolean(Constants.NEED_WX_CLOSE, true)
//                        }, Constants.DEFAULT_FLAG)
                        nav2Act(RoutePath.PATH_VI_TRADING_FLOOR)
                    }
                })
            }
            vClickId.click {
                val userInfo = LiveApp.getUserInfo()
                userInfo?.let { user ->
                    requireContext().textCopyThenPost(user.niceNum ?: "${user.id}")
                }
            }
            ivSignIn.click {
                nav2Act(RoutePath.PATH_ATTENDANCES)
            }
            llMessage.click {
                talkingClickEvent("我的通知")
                nav2Act(RoutePath.PATH_MESSAGE)
            }
        }
    }

    private fun go2Vip() {
        nav2Act(RoutePath.PATH_VIP)
    }

    private fun modifyAvatar() {
        checkAndRequestPermissions()
    }

    companion object {
        @JvmStatic
        fun newInstance() = TabMineFragment()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun event(event: EventBean) {
        when (event.eventCode) {
            EventCode.EVENT_LOGIN_SUCCESS -> {
                vm.value.getHelpCenter()
                resetUserInfo()
            }
        }
    }

    private val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            val writeExternalStorageGranted =
                permissions[Manifest.permission.WRITE_EXTERNAL_STORAGE] ?: false
            val readExternalStorageGranted =
                permissions[Manifest.permission.READ_EXTERNAL_STORAGE] ?: false

            if (writeExternalStorageGranted && readExternalStorageGranted) {
                openGallery()
            } else {
                showCustomToastFailed(WordUtil.getString(R.string.permission_storage_refused))
            }
        }

    private fun checkAndRequestPermissions() {
        // Check if the permissions are already granted
        val writeExternalStoragePermission = Manifest.permission.WRITE_EXTERNAL_STORAGE
        val readExternalStoragePermission = Manifest.permission.READ_EXTERNAL_STORAGE


        if (checkPermission(writeExternalStoragePermission) && checkPermission(
                readExternalStoragePermission
            )
        ) {
//            val intent = Intent(requireActivity(), GalleryActivity::class.java)
//            myActivityResultLauncher.launch(intent)
            openGallery()
        } else {
            // Permissions are not granted, request them from the user.
            requestPermissions()
        }
    }

    private fun openGallery() {
        context?.let {
            PictureUtil.openGallery(it, object : PictureUtil.PictureCallback {
                override fun onResult(result: List<ImageInfoBean>) {
                    if (result.isNotEmpty()) {
                        val path = result[0].path
                        if (path.isNotBlank()) {
                            vm.value.uploadAvatar(path)
                        }
                    }
                }
            }, 1)
        }
    }

    private fun checkPermission(permission: String): Boolean {
        return ContextCompat.checkSelfPermission(
            requireContext(),
            permission
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestPermissions() {
        val permissions = arrayOf(
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
        requestPermissionLauncher.launch(permissions)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        unregisterEvent()
    }
}