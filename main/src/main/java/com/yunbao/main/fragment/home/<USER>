package com.yunbao.main.fragment.home

import androidx.core.os.bundleOf
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.whenResumed
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.GridLayoutManager
import com.yunbao.common.Constants
import com.yunbao.common.EventCode
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.bean.EventBean
import com.yunbao.common.bean.LiveListBean
import com.yunbao.common.bean.LiveRoomInfo
import com.yunbao.common.interfaces.CommonCallback
import com.yunbao.common.reactivehttp.base.BaseRectFragment
import com.yunbao.common.utils.ClickHelper
import com.yunbao.common.utils.getLiveRoomItemNum
import com.yunbao.common.utils.indexOfFirstOrZero
import com.yunbao.common.utils.log
import com.yunbao.common.utils.nav2Act
import com.yunbao.common.utils.postEvent
import com.yunbao.common.utils.registerEvent
import com.yunbao.common.utils.saveRoomListData
import com.yunbao.common.utils.saveRoomListDataAsync
import com.yunbao.common.utils.toJson
import com.yunbao.common.utils.unregisterEvent
import com.yunbao.main.databinding.FragmentHomeFollowBinding
import com.yunbao.main.http.MainHttpConsts
import com.yunbao.main.http.MainHttpUtil
import com.yunbao.main.utils.CacheDataUtil
import com.yunbao.main.views.home.following.HomeFollowingAdapter
import com.yunbao.main.views.home.following.HomeFollowingItem
import com.yunbao.main.views.home.recommend.HomeRecommendationFooterAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class HomeFollowViewModel: ViewModel() {
    var page = HomeFollowFragment.INITIAL_PAGE

    private val _liveRoomLoadNew = MutableStateFlow<List<HomeFollowingItem>?>(null)
    val liveRoomLoadNew = _liveRoomLoadNew.asStateFlow().filterNotNull()

    private val _liveRoomRandomly = MutableSharedFlow<List<HomeFollowingItem>>()
    val liveRoomRandomly = _liveRoomRandomly.asSharedFlow()

    private val _hasNextPage = MutableStateFlow<Boolean?>(null)
    val hasNextPage = _hasNextPage.asStateFlow().filterNotNull()

    val isLiveRoomEmpty: Boolean get() = _liveRoomLoadNew.value.isNullOrEmpty()

    fun fetchData() {
        viewModelScope.launch {
            delay(500)
            val followed = async { fetchFollowed() }
            val forYou = async {
                fetchForYou().let { bean ->
                    if (bean == null) {
                        return@let emptyList<HomeFollowingItem.ForYou>()
                    }
                    if (page == HomeFollowFragment.INITIAL_PAGE) {
                        CacheDataUtil.cacheHomeFollowForYouData(bean.list)
                    }
                    val count = bean.count
                    val totalPage = count / HomeEggFragment.PAGE_SIZE + if (count % HomeEggFragment.PAGE_SIZE == 0) 0 else 1
                    val hasNextPage = page < totalPage
                    _hasNextPage.emit(hasNextPage)

                    return@let bean.list.map { HomeFollowingItem.ForYou(it) }
                }
            }
            val rooms = withContext(Dispatchers.IO) {
                listOf(HomeFollowingItem.FollowedTitle) + followed.await() +
                        listOf(HomeFollowingItem.ForYouTitle) + forYou.await()
            }
            _liveRoomLoadNew.emit(rooms)
        }
    }

    private suspend fun fetchFollowed(): List<HomeFollowingItem> = suspendCoroutine { continuation ->
        MainHttpUtil.getLiveFocus(object : CommonCallback<LiveListBean>() {
            override fun callback(bean: LiveListBean?) {
                if (bean == null) {
                    continuation.resume(emptyList())
                    return
                }
                CacheDataUtil.cacheHomeFollowData(bean.list)
                val list = bean.list.map { HomeFollowingItem.Followed(it) }
                continuation.resume(list)
            }

            override fun onError() {
                continuation.resume(emptyList())
            }
        })
    }

    private suspend fun fetchForYou() = suspendCoroutine { continuation ->
        MainHttpUtil.getLiveRecommendList(page, HomeFollowFragment.PAGE_SIZE, object : CommonCallback<LiveListBean>() {
            override fun callback(bean: LiveListBean?) {
                continuation.resume(bean)
            }

            override fun onError() {
                continuation.resume(null)
            }
        })
    }

    fun fetchForYouRandomly() {
        viewModelScope.launch {
            val rooms = withContext(Dispatchers.IO) { getForYouRandomly() }
            _liveRoomRandomly.emit(rooms)
        }
    }

    private suspend fun getForYouRandomly(): List<HomeFollowingItem> = suspendCoroutine { continuation ->
        MainHttpUtil.getLiveChangeAPatchList(HomeFollowFragment.PAGE_SIZE, object : CommonCallback<LiveListBean>() {
            override fun callback(bean: LiveListBean?) {
                if (bean == null) {
                    continuation.resume(emptyList())
                    return
                }

                val items = bean.list.map { HomeFollowingItem.ForYou(it) }
                continuation.resume(items)
            }

            override fun onError() {
                continuation.resume(emptyList())
            }
        })
    }

}

/**
 * 首页-关注
 */
class HomeFollowFragment : BaseRectFragment() {
    private val footerAdapter: HomeRecommendationFooterAdapter = HomeRecommendationFooterAdapter()
    private val mFollowingAdapter: HomeFollowingAdapter = createFollowingAdapter()
    private val mAdapter: ConcatAdapter = ConcatAdapter(mFollowingAdapter)
    private val viewModel by lazy {
        ViewModelProvider(this)[HomeFollowViewModel::class.java]
    }

    override val bind by getBind<FragmentHomeFollowBinding>()

    override fun initView() {
        registerEvent()
        bind.rvList.apply {
            setHomeDecoration()
            layoutManager = createGridLayoutManager()
            adapter = mAdapter
        }
        bind.refreshView.setOnRefreshListener {
            refreshData()
        }
        log("TabHomeFragment: HomeFollowFragment (initView)")
        viewLifecycleOwner.lifecycleScope.launch {
            launch {
                whenResumed {
                    viewModel.liveRoomLoadNew.collect { rooms ->
                        log("TabHomeFragment: HomeFollowFragment (liveRoomLoadNew)")
                        if (bind.rvList.adapter == null) {
                            bind.rvList.adapter = mAdapter
                        }
                        mFollowingAdapter.submitList(rooms)
                        bind.refreshView.finishRefresh()
                    }
                }
            }
            launch {
                whenResumed {
                    viewModel.liveRoomRandomly.collect { rooms ->
                        log("TabHomeFragment: HomeFollowFragment (liveRoomRandomly)")
                        val list = mFollowingAdapter.currentList()
                            .filterNot { it is HomeFollowingItem.ForYou }
                            .plus(rooms)
                        mFollowingAdapter.submitList(list)
                    }
                }
            }
            launch {
                whenResumed {
                    viewModel.hasNextPage.collectLatest { hasNextPage ->
                        if (hasNextPage) mAdapter.removeAdapter(footerAdapter)
                        else mAdapter.addAdapter(footerAdapter)
                        footerAdapter.bind(hasNextPage)
                        bind.refreshView.finishRefresh()
//                        bind.refreshView.setEnableLoadMore(hasNextPage)
                    }
                }
            }
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                launch {
                    if (LiveApp.token.isNotBlank() && viewModel.isLiveRoomEmpty && mFollowingAdapter.currentList().isEmpty()) {
                        refreshData()
                        log("TabHomeFragment: HomeFollowFragment (${lifecycle.currentState})")
                    }
                }
            }
        }
    }

    private fun refreshData() {
        viewModel.page = INITIAL_PAGE
        viewModel.fetchData()
    }

    private fun refreshForYou() {
        viewModel.page = INITIAL_PAGE
        viewModel.fetchForYouRandomly()
    }

    private fun createFollowingAdapter(): HomeFollowingAdapter {
        return HomeFollowingAdapter(object : HomeFollowingAdapter.ItemClickListener {
            override fun followMoreClicked() {
                nav2Act(RoutePath.PATH_FOLLOW)
            }

            override fun refreshForYouListClicked() {
                refreshForYou()
            }

            override fun followedClicked(room: LiveListBean.ListBean) {
                val rooms = mFollowingAdapter.currentList()
                    .filterIsInstance<HomeFollowingItem.Followed>()
                    .map { it.room }
                navigateToLivePage(room, rooms)
            }

            override fun forYouClicked(room: LiveListBean.ListBean) {
                val rooms = mFollowingAdapter.currentList()
                    .filterIsInstance<HomeFollowingItem.ForYou>()
                    .map { it.room }
                navigateToLivePage(room, rooms)
            }

        })
    }

    private fun createGridLayoutManager(): GridLayoutManager {
        val liveRoomItemNum = getLiveRoomItemNum()
        HomeFollowingAdapter.ViewType.FOLLOWED_LIVE_ROOM.numOfItemInRow = liveRoomItemNum
        HomeFollowingAdapter.ViewType.FOR_YOU_LIVE_ROOM.numOfItemInRow = liveRoomItemNum
        val spanCount = HomeFollowingAdapter.ViewType.values()
            .map { it.numOfItemInRow }
            .fold(1) { multiples, current ->
                multiples * current
            }

        return GridLayoutManager(context, spanCount).apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    if (mAdapter.adapters.contains(footerAdapter) && position == mAdapter.itemCount - 1) {
                        return spanCount
                    }
                    val viewTypeAsInt = mFollowingAdapter.getItemViewType(position)
                    val viewType = HomeFollowingAdapter.ViewType.values().first { it.ordinal == viewTypeAsInt }
                    return spanCount / viewType.numOfItemInRow
                }
            }
        }
    }

    private fun navigateToLivePage(
        target: LiveListBean.ListBean,
        rooms: List<LiveListBean.ListBean>
    ) {
        if (ClickHelper.isFastDoubleClick()) return
        if(!LiveApp.isLoginResult()) {
            postEvent(EventBean(EventCode.EVENT_NEED_LOGIN))
            return
        }
        runBlocking {
            val position = rooms.indexOfFirstOrZero { it.id == target.id }
            val roomList = rooms.map { liveBean ->
                LiveRoomInfo(roomId = liveBean.id, thumb = liveBean.thumb)
            }
            saveRoomListDataAsync(roomList.toJson())
            nav2Act(
                RoutePath.PATH_LIVE_ROOM,
                bundleOf(
                    Constants.CUR_POSITION to position,
                    Constants.INDEX to (viewModel.page + 1)
                )
            )
        }
    }

    private fun release() {
        MainHttpUtil.cancel(MainHttpConsts.GET_LIVE_FOCUS)
        EventBus.getDefault().unregister(this)
    }

    @Subscribe
    fun event(event: EventBean) {
        when (event.eventCode) {
            EventCode.EVENT_HOME_DOUBLE_CLICK -> {
                if (isResumed && mFollowingAdapter.currentList().isNotEmpty()) {
                    bind.rvList.smoothScrollToPosition(0)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        release()
        unregisterEvent()
    }

    companion object {
        @JvmStatic
        fun newInstance() = HomeFollowFragment()

        const val INITIAL_PAGE = 1
        const val PAGE_SIZE = 10
    }
}