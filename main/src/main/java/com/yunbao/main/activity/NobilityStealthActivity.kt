package com.yunbao.main.activity

import com.alibaba.android.arouter.facade.annotation.Route
import com.yunbao.common.Constants
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.bean.ConfigInfo
import com.yunbao.common.reactivehttp.base.BaseRectActivity
import com.yunbao.common.utils.getConfig
import com.yunbao.common.utils.setConfig
import com.yunbao.common.utils.ternary
import com.yunbao.main.R
import com.yunbao.main.databinding.ActivityNobilityStealthBinding
import com.yunbao.main.reactivehttp.viewmodel.SettingVM
import com.yunbao.main.utils.LocalDataUtil

/**
 * 贵族隐身
 */
@Route(path = RoutePath.PATH_NOBILITY_STEALTH)
class NobilityStealthActivity : BaseRectActivity() {
    private lateinit var config: ConfigInfo
    private var isInit = false
    private var isEnterChange = false
    private var isRankChange = false

    override val bind by getBind<ActivityNobilityStealthBinding>()

    override fun initView() {
        setTitle(R.string.set_nobility_stealth)
        config = getConfig()
        bind.stvAdmission.switchIsChecked = config.enterHide
        bind.stvList.switchIsChecked = config.rankHide
        LiveApp.getUserInfo()?.let {
            bind.stvAdmission.switchIsChecked = it.enterStealth == Constants.YES
            bind.stvList.switchIsChecked = it.rankStealth == Constants.YES
        }
        bind.apply {
            stvAdmission.setSwitchCheckedChangeListener { _, isChecked ->
                config.entranceMsgStealth = ternary(isChecked, 1, 0)
                setConfig(config)
                if (!isInit) {
                    isEnterChange = true
                    isRankChange = false
                    vm.setStealthWithLoading(LocalDataUtil.CONFIG_ENTER_HIDE, config)
                }
            }
            stvList.setSwitchCheckedChangeListener { _, isChecked ->
                config.rankStealth = ternary(isChecked, 1, 0)
                setConfig(config)
                if (!isInit) {
                    isRankChange = true
                    isEnterChange = false
                    vm.setStealthWithLoading(LocalDataUtil.CONFIG_RANK_HIDE, config)
                }
            }
        }
    }

    override fun initData() {
//        vm.getUserInfo(getUId())
    }

    private val vm by getViewModel<SettingVM> {
//        userInfo.observe(this@NobilityStealthActivity) {
//            isInit = true
//            bind.stvAdmission.switchIsChecked = it.enterStealth == Constants.YES
//            bind.stvList.switchIsChecked = it.rankStealth == Constants.YES
//            isInit = false
//        }

        setResult.observe(this@NobilityStealthActivity) {
            if (it != 0) {
                //更新失败，复原
                isInit = true
                if (isEnterChange) {
                    bind.stvAdmission.switchIsChecked = !bind.stvAdmission.switchIsChecked
                }
                if (isRankChange) {
                    bind.stvList.switchIsChecked = !bind.stvList.switchIsChecked
                }
                isInit = false
            }
            isRankChange = false
            isEnterChange = false
        }
    }
}