package com.yunbao.main.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.CountDownTimer
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContract
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.ktx.immersionBar
import com.sahooz.library.countrypicker.Country
import com.yunbao.common.CommonAppContext
import com.yunbao.common.Constants
import com.yunbao.common.EventCode
import com.yunbao.common.LiveApp
import com.yunbao.common.RoutePath
import com.yunbao.common.bean.EventBean
import com.yunbao.common.reactivehttp.base.BaseRectActivity
import com.yunbao.common.utils.*
import com.yunbao.main.R
import com.yunbao.main.databinding.ActivityPhoneAuthBinding
import com.yunbao.main.dialog.CountryPickerDialogFragment
import com.yunbao.main.reactivehttp.viewmodel.LoginVM

/**
 * 手机认证
 */
@Route(path = RoutePath.PATH_PHONE_AUTH)
class PhoneAuthActivity : BaseRectActivity() {
    private var countDownTimer: CountDownTimer? = null
    private lateinit var pickerLauncher: ActivityResultLauncher<Intent>

    override val bind by getBind<ActivityPhoneAuthBinding>()

    @SuppressLint("SetTextI18n")
    override fun initView() {
        setTitle(R.string.phone_auth)
        ivBack?.setImageResource(R.drawable.skin_icon_back_white)
        tvTitle?.setTextColor(ContextCompat.getColor(this, R.color.skin_color_white))
        val phone = LiveApp.getUserInfo()?.phone?:""
        if (phone.isNotBlank()) {
            bind.waitVerify.visibility = View.GONE
            bind.alreadyVerify.visibility = View.VISIBLE
            try {
                bind.tvPhone.text = phone.formatPhone()
            } catch (e: Exception) {
            }
        } else {
            bind.waitVerify.visibility = View.VISIBLE
            bind.alreadyVerify.visibility = View.GONE
        }
        pickerLauncher =
            registerForActivityResult(object : ActivityResultContract<Intent, String>() {
                override fun createIntent(context: Context, input: Intent): Intent {
                    return input
                }

                override fun parseResult(resultCode: Int, intent: Intent?): String {
                    if (resultCode == Activity.RESULT_OK && intent != null) {
                        return intent.getStringExtra(Constants.COUNTRY) ?: ""
                    }
                    return ""
                }
            }) { result ->
                if (result.isNotBlank()) {
                    val split = result.split(",")
                    if (split.isNotEmpty()) {
                        bind.tvCounty.text = split[0]
                        bind.tvAreaCode.text = split[1]
                    }
                }
            }
    }

    private val vm by getViewModel<LoginVM> {
        getCodeResult.observe(this@PhoneAuthActivity) {
            if (it) {
                showCustomToastSuccess(WordUtil.getString(R.string.code_sent))
                countDownTimer = object : CountDownTimer(Constants.SMS_CODE_INTERVAL, 1000) {
                    override fun onTick(millisUntilFinished: Long) {
                        if (!<EMAIL>) {
                            if (millisUntilFinished >= 1000) {
                                bind.tvGetCode.isEnabled = false
                                bind.tvGetCode.text =
                                    getString(R.string.reacquire_ph, millisUntilFinished / 1000)
                            } else {
                                bind.tvGetCode.isEnabled = true
                                bind.tvGetCode.text = getString(R.string.reg_get_code_again)
                            }
                        }
                    }

                    override fun onFinish() {

                    }
                }
                countDownTimer?.start()
            } else {
                bind.tvGetCode.text = getString(R.string.reg_get_code_again)
            }
        }
        bindPhoneResult.observe(this@PhoneAuthActivity) {
            postEvent(EventBean(EventCode.EVENT_USER_INFO_CHANGE))
            showCustomToastSuccess(WordUtil.getString(R.string.auth_completed))
            finish()
        }
    }

    override fun clickEvent() {
        bind.apply {
            ivBack?.click {
                onBackPressed()
            }
            tvGetCode.click {
                if (ClickHelper.isFastDoubleClick()) return@click
                val phone = edtPhone.text.toString().trim()
                if (phone.isBlank()) {
                    showCustomToastFailed(WordUtil.getString(R.string.login_input_phone))
                    return@click
                }
                vm.getCode(phone)
            }
            tvAuth.click {
                if (ClickHelper.isFastDoubleClick()) return@click
                val phone = edtPhone.text.toString().trim()
                if (phone.isBlank()) {
                    showCustomToastFailed(WordUtil.getString(R.string.login_input_phone))
                    return@click
                }
                //校验手机号
                val code = edtCode.text.toString().trim()
                if (code.isBlank()) {
                    showCustomToastFailed(WordUtil.getString(R.string.reg_input_code))
                    return@click
                }
                vm.bindPhone(phone, code, bind.tvAreaCode.text.toString())
            }
            tvCounty.click {
                var dialog = CountryPickerDialogFragment.newInstance(it.text.toString().trim())
                dialog.setDataCallbackListener(object : CountryPickerDialogFragment.DataCallbackListener {
                    override fun onCountrySelected(item: Country) {
                        bind.tvCounty.text = item.name
                        bind.tvAreaCode.text = "+${item.code}"
                    }
                })
                showDialogFragment(dialog)
            }
        }
    }

    override fun initImmersionBar() {
        super.initImmersionBar()
        immersionBar {
            titleBar(toolbar)
            statusBarDarkFont(false)
        }
    }

    override fun onDestroy() {
        countDownTimer?.cancel()
        countDownTimer = null
        super.onDestroy()
    }

    companion object {
        @JvmStatic
        fun forward() {
            val intent = Intent(CommonAppContext.instance(), PhoneAuthActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            CommonAppContext.instance().startActivity(intent)
        }
    }
}