package com.yunbao.main.activity

import android.graphics.Color
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.ktx.immersionBar
import com.yunbao.common.RoutePath
import com.yunbao.common.reactivehttp.base.BaseRectActivity
import com.yunbao.common.utils.MagicIndicatorScrollListener
import com.yunbao.common.utils.ViewPager2Helper
import com.yunbao.common.utils.initScrollIndicator
import com.yunbao.main.R
import com.yunbao.main.adapter.ViewPagerRankingAdapter
import com.yunbao.main.databinding.ActivityRankingPageBinding

@Route(path = RoutePath.PATH_RANKING_PAGE)
class RankingPageActivity : BaseRectActivity() {

    override val bind by getBind<ActivityRankingPageBinding>()

    companion object {
        const val ANCHOR_TAB = 0
        const val TYRANT_TAB = 1
        const val PK_TAB = 2
    }

    override fun initView() {
        val titles = resources.getStringArray(R.array.ranking_tab).toList()
        bind.indicator.initScrollIndicator(
            isAdjustMode = true,
            context = this@RankingPageActivity,
            titles = titles,
            isScale = true,
            sTextColor = Color.parseColor("#ffffff"),
            nTextColor = Color.parseColor("#BDffffff"),
            textSize = 16f,
            isBold = true,
            indicatorColor = Color.TRANSPARENT,
            listener = object : MagicIndicatorScrollListener {
                override fun selected(position: Int) {
                    bind.viewPager.currentItem = position
                }
            }
        )
        ViewPager2Helper.bind(bind.indicator, bind.viewPager)
        bind.viewPager.adapter = ViewPagerRankingAdapter(this@RankingPageActivity)
        bind.viewPager.offscreenPageLimit = 1
        bind.viewPager.isUserInputEnabled = false
    }

    override fun initImmersionBar() {
        super.initImmersionBar()
        immersionBar {
            titleBar(R.id.rlTitle)
            statusBarDarkFont(false)
        }
    }

    override fun clickEvent() {
        bind.ivBack.setOnClickListener {
            finish()
        }
    }
}