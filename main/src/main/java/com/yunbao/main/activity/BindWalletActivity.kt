package com.yunbao.main.activity

import android.text.TextUtils
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ColorUtils
import com.github.gzuliyujiang.wheelpicker.OptionPicker
import com.yunbao.common.Constants
import com.yunbao.common.EventCode
import com.yunbao.common.RoutePath
import com.yunbao.common.bean.EventBean
import com.yunbao.common.reactivehttp.base.BaseRectActivity
import com.yunbao.common.utils.WordUtil
import com.yunbao.common.utils.click
import com.yunbao.common.utils.postEvent
import com.yunbao.common.utils.routerInject
import com.yunbao.common.utils.showCustomToastFailed
import com.yunbao.common.utils.showCustomToastSuccess
import com.yunbao.main.R
import com.yunbao.main.bean.WalletType
import com.yunbao.main.databinding.ActivityBindWalletBinding
import com.yunbao.main.reactivehttp.viewmodel.WithdrawVM
import java.util.regex.Pattern


/**
 * 添加钱包
 */
@Route(path = RoutePath.PATH_BIND_WALLET)
class BindWalletActivity : BaseRectActivity() {

    @Autowired(name = Constants.ID)
    @JvmField
    var typeId: String = ""

    var walletPosition = 0

    private var type1: WalletType? = null
    var walletTypeList = mutableListOf<WalletType>()

    override val bind by getBind<ActivityBindWalletBinding>()

    override fun initView() {
        routerInject()
        setTitle(R.string.add_wallet)
    }

    override fun initData() {
        vm.getWalletTypeList()
    }

    private val vm by getViewModel<WithdrawVM> {
        walletTypeList.observe(this@BindWalletActivity) {
            <EMAIL> {
                clear()
                addAll(it)
            }
            if (typeId.isNotEmpty()) {
                for (i in 0 until it.size) {
                    if (it[i].id == typeId) {
                        walletPosition = i
                        bind.tvWalletType.text = it[i].walletName
                    }
                }
            }
            val richText = ""
            if(!TextUtils.isEmpty(richText)){
                bind.webView.isVerticalScrollBarEnabled = false
                bind.webView.isHorizontalFadingEdgeEnabled = false
                bind.webView.setBackgroundColor(ColorUtils.getColor(R.color.transparent))
                bind.webView.loadDataWithBaseURL(null, richText, "text/html", "utf-8", null)
            }
        }

        bindResult.observe(this@BindWalletActivity) {
            if (it) {
                showCustomToastSuccess(WordUtil.getString(R.string.bind_wallet_success))
                postEvent(EventBean(EventCode.EVENT_BIND_WALLET_SUCCESS))
                finish()
            } else {
                bind.tvBind.shapeDrawableBuilder.setSolidColor(
                    ContextCompat.getColor(
                        this@BindWalletActivity,
                        R.color.skin_color_11cfff
                    )
                ).intoBackground()
                bind.tvBind.isEnabled = true
            }
        }

        requestFinish.observe(this@BindWalletActivity) {
            //bind.refreshLayout.finishRefresh()
        }
    }

    override fun clickEvent() {
        bind.tvWalletType.click {
            if (walletTypeList.isEmpty()) {
                showCustomToastFailed(WordUtil.getString(R.string.data_empty))
                return@click
            }
            val picker = OptionPicker(this)
            picker.setData(walletTypeList.map { it.walletName })
            picker.setDefaultPosition(walletPosition)
            picker.setOnOptionPickedListener { position, _ ->
                type1 = walletTypeList[position]
                bind.tvWalletType.text = type1?.walletName
            }
            picker.wheelLayout.setOnOptionSelectedListener { position, item ->
                walletPosition = position
                picker.titleView.text = picker.wheelView.formatItem(position)
            }
            picker.show()
        }
        bind.tvBind.click {
            if (walletTypeList.isEmpty()) {
                showCustomToastFailed(WordUtil.getString(R.string.sel_wallet_type))
                return@click
            }
            val type = type1?.walletName ?: ""
            if (type.isBlank() && typeId.isEmpty()) {
                showCustomToastFailed(WordUtil.getString(R.string.sel_wallet_type))
                return@click
            }
            val walletAddress = bind.edtWalletAddress.text.toString().trim()
            if (walletAddress.isBlank()) {
                showCustomToastFailed(WordUtil.getString(R.string.input_wallet_address))
                return@click
            }
            var matcher = Pattern.compile("[0-9a-zA-Z]+").matcher(walletAddress)
            if (!matcher.matches()) {
                showCustomToastFailed(WordUtil.getString(R.string.wallet_address_format_error))
                return@click
            }
            bind.tvBind.isEnabled = false
            bind.tvBind.shapeDrawableBuilder.setSolidColor(
                ContextCompat.getColor(
                    this@BindWalletActivity,
                    R.color.skin_color_99
                )
            ).intoBackground()
            vm.bindWalletInfo(walletTypeList[walletPosition].id, walletAddress)
        }
    }

}