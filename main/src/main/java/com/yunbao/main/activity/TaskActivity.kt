package com.yunbao.main.activity

import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.ktx.immersionBar
import com.yunbao.common.Constants
import com.yunbao.common.RoutePath
import com.yunbao.common.reactivehttp.base.BaseRectActivity
import com.yunbao.common.utils.click
import com.yunbao.common.utils.routerInject
import com.yunbao.main.R
import com.yunbao.main.databinding.ActivityTaskBinding
import com.yunbao.main.fragment.main.TabTaskFragment

@Route(path = RoutePath.PATH_TASK)
class TaskActivity : BaseRectActivity() {
    @Autowired(name = Constants.INDEX)
    @JvmField
    var index: Int = 0

    override val bind by getBind<ActivityTaskBinding>()

    override fun initView() {
        routerInject()

        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.fl_container, TabTaskFragment.newInstance(index))
        ft.commitAllowingStateLoss()
    }

    override fun initImmersionBar() {
        super.initImmersionBar()
        immersionBar {
            titleBar(bind.toolbar)
        }
    }

    override fun clickEvent() {
        bind.ivBack.click { onBackPressed() }
    }
}