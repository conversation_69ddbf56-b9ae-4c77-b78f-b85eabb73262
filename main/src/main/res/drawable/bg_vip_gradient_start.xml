<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:width="@dimen/size_20dp" android:height="@dimen/size_90dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#4dffffff"
                android:centerColor="#4dffffff"
                android:endColor="#4dffffff"
                android:angle="90"/>

            <stroke android:width="1dp" android:color="#4dffffff" />

            <corners android:bottomLeftRadius="0dp"
                android:bottomRightRadius="10dp"
                android:topLeftRadius="0dp"
                android:topRightRadius="10dp" />
        </shape>
    </item>
</selector>