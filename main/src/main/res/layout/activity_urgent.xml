<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/skin_color_white"
    android:orientation="vertical"
    tools:context="com.yunbao.main.activity.UrgentActivity">

    <include layout="@layout/view_title" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:overScrollMode="never"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvMsg"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp24"
                android:layout_marginTop="@dimen/dp20"
                android:gravity="center_horizontal"
                android:textColor="@color/skin_color_red"
                android:textSize="@dimen/sp15"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="@string/default_long_text" />

            <View
                android:id="@+id/v1"
                android:layout_width="@dimen/dp50"
                android:layout_height="@dimen/dp3"
                android:layout_marginTop="@dimen/dp50"
                android:background="@color/skin_color_red"
                app:layout_constraintEnd_toStartOf="@+id/tvTip"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMsg" />

            <TextView
                android:id="@+id/tvTip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp15"
                android:text="@string/line_selection"
                android:textColor="@color/skin_color_red"
                android:textSize="@dimen/sp18"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/v1"
                app:layout_constraintEnd_toStartOf="@+id/v2"
                app:layout_constraintStart_toEndOf="@+id/v1"
                app:layout_constraintTop_toTopOf="@+id/v1" />

            <View
                android:id="@+id/v2"
                android:layout_width="@dimen/dp50"
                android:layout_height="@dimen/dp3"
                android:background="@color/skin_color_red"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvTip"
                app:layout_constraintTop_toTopOf="@+id/v1" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvLine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp30"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTip"
                app:spanCount="2"
                tools:itemCount="6"
                tools:listitem="@layout/item_urgent_line" />

            <LinearLayout
                android:id="@+id/linRefresh"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp44"
                android:layout_marginHorizontal="@dimen/dp28"
                android:layout_marginTop="@dimen/dp20"
                android:background="@drawable/bg_line_refresh"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rvLine">

                <ImageView
                    android:layout_width="@dimen/dp18"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/icon_refresh" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:text="@string/line_refresh"
                    android:textColor="@color/skin_color_white"
                    android:textSize="@dimen/sp15"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/linService"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp44"
                android:layout_marginHorizontal="@dimen/dp28"
                android:layout_marginTop="@dimen/dp20"
                android:layout_marginBottom="@dimen/dp50"
                android:background="@drawable/bg_line_service"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linRefresh">

                <ImageView
                    android:layout_width="@dimen/dp18"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/icon_customer_service" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:text="@string/enter_customer_service"
                    android:textColor="@color/c_33"
                    android:textSize="@dimen/sp15"
                    android:textStyle="bold" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>