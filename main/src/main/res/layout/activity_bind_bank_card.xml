<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/skin_bg_tab_mine"
    tools:context=".activity.BindBankCardActivity">

    <include layout="@layout/view_title" />

    <com.hjq.shape.layout.ShapeLinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp15"
        android:orientation="vertical"
        app:shape_radius="@dimen/dp15"
        app:shape_solidColor="@color/skin_color_white">

        <TextView
            android:id="@+id/txtCardholder"
            style="@style/style_text_33_bold_15"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp20"
            android:text="@string/cardholder"
            app:layout_constraintBottom_toTopOf="@+id/vDivide1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title" />

        <com.hjq.shape.view.ShapeEditText
            android:id="@+id/edtCardholder"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp45"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp20"
            android:paddingStart="@dimen/dp10"
            android:paddingEnd="@dimen/dp10"
            android:hint="请输入持卡人姓名"
            android:textColorHint="@color/c_99"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_28"
            app:shape_radius="@dimen/dp10"
            app:shape_solidColor="@color/color_F7F8F9" />

        <TextView
            android:id="@+id/txtCardNumber"
            style="@style/style_text_33_bold_15"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp15"
            android:text="@string/bank_card_number" />

        <com.hjq.shape.view.ShapeEditText
            android:id="@+id/edtCardNumber"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp45"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp20"
            android:paddingStart="@dimen/dp10"
            android:paddingEnd="@dimen/dp10"
            android:inputType="number"
            android:hint="请输入银行卡号"
            android:textColorHint="@color/c_99"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_28"
            app:shape_radius="@dimen/dp10"
            app:shape_solidColor="@color/color_F7F8F9" />

        <TextView
            android:id="@+id/txtDepositary"
            style="@style/style_text_33_bold_15"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp15"
            android:text="@string/depositary_bank" />

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvDepositary"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp45"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp20"
            android:paddingStart="@dimen/dp10"
            android:paddingEnd="@dimen/dp10"
            android:hint="@string/sel_depositary_bank"
            android:textColorHint="@color/c_99"
            android:textColor="@color/black"
            android:gravity="center_vertical"
            android:textSize="@dimen/text_size_28"
            android:drawableEnd="@mipmap/icon_comment_expand"
            app:shape_radius="@dimen/dp10"
            app:shape_solidColor="@color/color_F7F8F9" />

        <TextView
            android:id="@+id/txtBranch"
            style="@style/style_text_33_bold_15"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp15"
            android:text="@string/bank_branch"
            app:layout_constraintBottom_toTopOf="@+id/vDivide4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vDivide3" />

        <com.hjq.shape.view.ShapeEditText
            android:id="@+id/edtBranch"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp45"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp20"
            android:layout_marginBottom="@dimen/dp20"
            android:paddingStart="@dimen/dp10"
            android:paddingEnd="@dimen/dp10"
            android:hint="请输入开户支行"
            android:textColorHint="@color/c_99"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_28"
            app:shape_radius="@dimen/dp10"
            app:shape_solidColor="@color/color_F7F8F9" />
    </com.hjq.shape.layout.ShapeLinearLayout>

    <com.hjq.shape.view.ShapeTextView
        android:id="@+id/tvBind"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginStart="@dimen/dp40"
        android:layout_marginTop="@dimen/dp40"
        android:layout_marginEnd="@dimen/dp40"
        android:gravity="center"
        android:text="确认绑定"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_28"
        app:shape_solidColor="@color/skin_color_11cfff"
        app:shape_radius="@dimen/dp22" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:text="@string/bind_remind"
        android:textColor="@color/c_99"
        android:textSize="@dimen/text_size_24"
      android:layout_gravity="center" />

</LinearLayout>