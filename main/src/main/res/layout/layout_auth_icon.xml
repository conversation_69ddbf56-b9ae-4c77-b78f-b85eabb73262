<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clAuthIcons"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/skin_color_white"
    android:paddingBottom="@dimen/dp36">

    <com.yunbao.main.views.ImageTextView
        android:id="@+id/itvLogin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp30"
        android:drawablePadding="@dimen/dp22"
        android:gravity="center_horizontal"
        android:text="手机登录"
        android:textColor="@color/c_33"
        android:textSize="@dimen/text_size_28"
        app:drawable="@drawable/skin_icon_auth_login"
        app:drawableHeight="62dp"
        app:drawableWidth="62dp"
        app:layout_constraintEnd_toStartOf="@+id/itvSafety"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:position="2" />

    <com.yunbao.main.views.ImageTextView
        android:id="@+id/itvSafety"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/dp22"
        android:gravity="center_horizontal"
        android:text="安全认证"
        android:textColor="@color/c_33"
        android:textSize="@dimen/text_size_28"
        app:drawable="@drawable/skin_icon_auth_verify"
        app:drawableHeight="62dp"
        app:drawableWidth="62dp"
        app:layout_constraintEnd_toStartOf="@+id/itvIncome"
        app:layout_constraintStart_toEndOf="@+id/itvLogin"
        app:layout_constraintTop_toTopOf="@+id/itvLogin"
        app:position="2" />

    <com.yunbao.main.views.ImageTextView
        android:id="@+id/itvIncome"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp30"
        android:drawablePadding="@dimen/dp22"
        android:gravity="center_horizontal"
        android:text="收益保障"
        android:textColor="@color/c_33"
        android:textSize="@dimen/text_size_28"
        app:drawable="@drawable/skin_icon_auth_safeguard"
        app:drawableHeight="62dp"
        app:drawableWidth="62dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/itvSafety"
        app:layout_constraintTop_toTopOf="@+id/itvLogin"
        app:position="2" />

</androidx.constraintlayout.widget.ConstraintLayout>