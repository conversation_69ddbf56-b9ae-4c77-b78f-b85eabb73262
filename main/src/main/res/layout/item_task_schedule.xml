<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp25"
    android:minHeight="65dp">

    <TextView
        android:id="@+id/tvTask"
        style="@style/style_text_auto_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoSizeMaxTextSize="14sp"
        android:gravity="center_vertical"
        android:maxWidth="180dp"
        android:textColor="@color/c_66"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@+id/clAward"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tvTime"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/default_name" />

    <com.hjq.shape.layout.ShapeConstraintLayout
        android:id="@+id/clAward"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp20"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTask"
        app:layout_constraintEnd_toStartOf="@+id/tvForward"
        app:layout_constraintStart_toEndOf="@+id/tvTask"
        app:shape_radius="@dimen/dp10"
        app:shape_solidColor="#ffeadd">

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="1dp"
            android:src="@drawable/ic_gold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvAward"
            style="@style/style_text_auto_size"
            android:layout_width="wrap_content"
            android:layout_marginStart="1dp"
            android:layout_marginEnd="3dp"
            android:gravity="center_vertical"
            android:textColor="#d64900"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/default_name" />
    </com.hjq.shape.layout.ShapeConstraintLayout>
    
    <TextView
        android:id="@+id/tvTime"
        app:layout_constraintTop_toBottomOf="@+id/tvTask"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvTask"
        android:layout_marginTop="@dimen/dp12"
        tools:text="@string/default_time"
        android:textSize="11sp"
        android:textColor="#b3b3b3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <com.hjq.shape.view.ShapeTextView
        android:id="@+id/tvForward"
        android:layout_width="75dp"
        android:layout_height="30dp"
        android:gravity="center"
        android:textColor="#ff0a8f"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shape_radius="@dimen/dp15"
        app:shape_strokeColor="#ff0a8f"
        app:shape_strokeSize="1dp"
        tools:text="@string/received" />

    <View
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@color/color_eeeeee"
        android:id="@+id/vDivide"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>