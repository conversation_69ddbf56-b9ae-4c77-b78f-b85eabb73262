<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rlPKLeft"
        android:layout_width="0dp"
        android:layout_height="@dimen/live_room_item_height"
        android:layout_marginEnd="@dimen/dp5"
        app:layout_constraintEnd_toStartOf="@+id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/coverLeft"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toTopOf="@+id/ivBottom"
            app:layout_constraintTop_toTopOf="parent"
            app:riv_corner_radius_top_left="15dp"
            app:riv_corner_radius_top_right="15dp"
            tools:src="@tools:sample/avatars" />
        <LinearLayout
            android:id="@+id/linLocationLeft"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_live_city_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <pl.droidsonroids.gif.GifImageView
                android:layout_width="wrap_content"
                android:layout_height="10dp"
                android:layout_marginEnd="3dp"
                android:adjustViewBounds="true"
                android:src="@drawable/skin_gif_ranking" />

            <TextView
                android:id="@+id/locationLeft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:textSize="9sp"
                android:textColor="@color/skin_color_night_default"
                tools:text="@string/default_name" />

        </LinearLayout>

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/titleLeft"
            style="@style/style_text_auto_size"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:autoSizeMaxTextSize="13sp"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp10"
            android:textColor="#fff"
            android:textSize="13sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/coverLeft"
            app:shape_solidColor="@color/black36"
            tools:text="@string/default_long_text" />

        <ImageView
            android:id="@+id/ivGameBgLeft"
            android:layout_width="84dp"
            android:layout_height="24dp"
            android:scaleType="fitXY"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/skin_bg_live_game_night"
            app:riv_corner_radius_top_left="@dimen/dp15" />

        <TextView
            android:id="@+id/gameTypeLeft"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#fff"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivGameBgLeft"
            app:layout_constraintEnd_toEndOf="@+id/ivGameBgLeft"
            app:layout_constraintStart_toStartOf="@+id/ivGameBgLeft"
            app:layout_constraintTop_toTopOf="@+id/ivGameBgLeft"
            tools:text="一分时时彩"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/roomTypeLeft"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp14"
            android:layout_marginStart="10dp"
            android:layout_marginTop="5dp"
            android:background="@drawable/bg_room_type"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="4dp"
            android:textSize="9sp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivGameBgLeft"
            android:textColor="@color/skin_color_night_default"
            tools:text="按时收费"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_toy_Left"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="@dimen/dp10"
            android:layout_marginTop="5dp"
            android:src="@drawable/ic_virabtion_feature"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/roomTypeLeft"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivBottom"
            android:layout_width="match_parent"
            android:layout_height="37dp"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@drawable/skin_bg_live_info" />

        <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
            android:id="@+id/ivAnchorAvatar"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginStart="9dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottom"
            app:layout_constraintStart_toStartOf="@+id/ivBottom"
            app:layout_constraintTop_toTopOf="@+id/ivBottom"
            app:qmui_is_circle="true"
            tools:src="@drawable/ph_avatar" />

        <TextView
            android:id="@+id/tvAnchorName"
            style="@style/style_text_auto_size"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="7dp"
            android:layout_marginEnd="@dimen/dp5"
            android:autoSizeMaxTextSize="13sp"
            android:gravity="center_vertical"
            android:textSize="13sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottom"
            app:layout_constraintEnd_toStartOf="@+id/ivOnlineIcon"
            app:layout_constraintStart_toEndOf="@+id/ivAnchorAvatar"
            app:layout_constraintTop_toTopOf="@+id/ivBottom"
            android:textColor="@color/skin_color_black_light"
            tools:text="@string/default_name" />

        <TextView
            android:id="@+id/onlineNumLeft"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="11dp"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottom"
            app:layout_constraintEnd_toEndOf="@+id/ivBottom"
            app:layout_constraintTop_toTopOf="@+id/ivBottom"
            android:textColor="@color/skin_color_night_default"
            tools:text="@string/default_number" />

        <ImageView
            android:id="@+id/ivOnlineIcon"
            android:layout_width="13dp"
            android:layout_height="16dp"
            android:layout_marginEnd="6dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottom"
            app:layout_constraintEnd_toStartOf="@+id/onlineNumLeft"
            app:layout_constraintTop_toTopOf="@+id/ivBottom"
            android:src="@drawable/skin_icon_follow4you" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rlPKRight"
        android:layout_width="0dp"
        android:layout_height="@dimen/live_room_item_height"
        android:layout_marginStart="@dimen/dp5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline"
        app:layout_constraintTop_toTopOf="parent">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/coverRight"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toTopOf="@+id/ivBottomRight"
            app:layout_constraintTop_toTopOf="parent"
            app:riv_corner_radius_top_left="15dp"
            app:riv_corner_radius_top_right="15dp"
            tools:src="@tools:sample/avatars" />

        <LinearLayout
            android:id="@+id/linLocationRight"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_live_city_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <pl.droidsonroids.gif.GifImageView
                android:layout_width="wrap_content"
                android:layout_height="10dp"
                android:layout_marginEnd="3dp"
                android:adjustViewBounds="true"
                android:src="@drawable/skin_gif_ranking" />

            <TextView
                android:id="@+id/locationRight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:textSize="9sp"
                android:textColor="@color/skin_color_night_default"
                tools:text="@string/default_name" />
        </LinearLayout>

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/titleRight"
            style="@style/style_text_auto_size"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:autoSizeMaxTextSize="13sp"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp10"
            android:textColor="#fff"
            android:textSize="13sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/coverRight"
            app:shape_solidColor="@color/black36"
            tools:text="@string/default_long_text" />

        <ImageView
            android:id="@+id/ivGameBgRight"
            android:layout_width="84dp"
            android:layout_height="24dp"
            android:scaleType="fitXY"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/skin_bg_live_game_night"
            app:riv_corner_radius_top_left="@dimen/dp15" />

        <TextView
            android:id="@+id/gameTypeRight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#fff"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivGameBgRight"
            app:layout_constraintEnd_toEndOf="@+id/ivGameBgRight"
            app:layout_constraintStart_toStartOf="@+id/ivGameBgRight"
            app:layout_constraintTop_toTopOf="@+id/ivGameBgRight"
            tools:text="一分时时彩"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/roomTypeRight"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp14"
            android:layout_marginStart="10dp"
            android:layout_marginTop="5dp"
            android:background="@drawable/bg_room_type"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="4dp"
            android:textSize="9sp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivGameBgRight"
            android:textColor="@color/skin_color_night_default"
            tools:text="按时收费"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_toy_Right"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="@dimen/dp10"
            android:layout_marginTop="5dp"
            android:src="@drawable/ic_virabtion_feature"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/roomTypeRight"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivBottomRight"
            android:layout_width="match_parent"
            android:layout_height="37dp"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@drawable/skin_bg_live_info" />

        <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
            android:id="@+id/ivAnchorAvatarRight"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginStart="9dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottomRight"
            app:layout_constraintStart_toStartOf="@+id/ivBottomRight"
            app:layout_constraintTop_toTopOf="@+id/ivBottomRight"
            app:qmui_is_circle="true"
            tools:src="@drawable/ph_avatar" />

        <TextView
            android:id="@+id/tvAnchorNameRight"
            style="@style/style_text_auto_size"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="7dp"
            android:layout_marginEnd="@dimen/dp5"
            android:autoSizeMaxTextSize="13sp"
            android:gravity="center_vertical"
            android:textSize="13sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottomRight"
            app:layout_constraintEnd_toStartOf="@+id/ivOnlineIconRight"
            app:layout_constraintStart_toEndOf="@+id/ivAnchorAvatarRight"
            app:layout_constraintTop_toTopOf="@+id/ivBottomRight"
            android:textColor="@color/skin_color_black_light"
            tools:text="@string/default_name" />

        <TextView
            android:id="@+id/onlineNumRight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="11dp"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottomRight"
            app:layout_constraintEnd_toEndOf="@+id/ivBottomRight"
            app:layout_constraintTop_toTopOf="@+id/ivBottomRight"
            android:textColor="@color/skin_color_night_default"
            tools:text="@string/default_number" />

        <ImageView
            android:id="@+id/ivOnlineIconRight"
            android:layout_width="13dp"
            android:layout_height="16dp"
            android:layout_marginEnd="6dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottomRight"
            app:layout_constraintEnd_toStartOf="@+id/onlineNumRight"
            app:layout_constraintTop_toTopOf="@+id/ivBottomRight"
            android:src="@drawable/skin_icon_follow4you" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/N_A"
        android:src="@drawable/skin_icon_pk_list"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>
