<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:layout_width="@dimen/dp24"
        android:layout_height="@dimen/dp24"
        android:src="@drawable/icon_message_select_all_normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/dp30"
        android:layout_marginStart="@dimen/dp15"
        android:id="@+id/iconCheck"
        >
    </ImageView>

    <ImageView
        android:id="@+id/ivEmailIcon"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        android:layout_marginStart="@dimen/dp15"
        android:src="@drawable/ic_notification_message"
        app:layout_constraintBottom_toBottomOf="@+id/vEmailMsg"
        app:layout_constraintStart_toEndOf="@id/iconCheck"
        app:layout_constraintTop_toTopOf="@+id/vEmailMsg" />

    <TextView
        android:id="@+id/tvEmailMsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp14"
        android:text="@string/email_msg"
        android:textColor="@color/c_33"
        android:textSize="@dimen/text_size_30"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/tvEmailContent"
        app:layout_constraintStart_toEndOf="@+id/ivEmailIcon"
        app:layout_constraintTop_toTopOf="@+id/vEmailMsg"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvEmailTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp16"
        android:textColor="@color/c_99"
        android:textSize="@dimen/text_size_20"
        app:layout_constraintBottom_toBottomOf="@+id/tvEmailMsg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvEmailMsg"
        tools:text="@string/default_time" />

    <WebView
        android:id="@+id/tvEmailContent"
        android:layout_width="0dp"
        android:layout_marginStart="-10dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginEnd="50dp"
        app:layout_constraintBottom_toBottomOf="@+id/vEmailMsg"
        app:layout_constraintEnd_toStartOf="@+id/tvEmailUnreadCount"
        app:layout_constraintStart_toStartOf="@+id/tvEmailMsg"
        app:layout_constraintTop_toBottomOf="@+id/tvEmailMsg" />

    <TextView
        android:id="@+id/tvEmailUnreadCount"
        android:layout_width="@dimen/dp22"
        android:layout_height="@dimen/dp22"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/skin_bg_mine_msg_count"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_22"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvEmailContent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvEmailContent"
        tools:text="999"
        tools:visibility="visible" />

    <View
        android:id="@+id/vEmailMsg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp80"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>