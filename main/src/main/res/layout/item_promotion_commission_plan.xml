<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp15"
    android:layout_marginVertical="@dimen/margin_8dp"
    android:background="@drawable/skin_bg_superiors_radius_15">

    <TextView
        android:id="@+id/textCommissionPlanTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/ic_promotion_line"
        android:drawablePadding="@dimen/dp4"
        android:gravity="center|start"
        android:padding="@dimen/dp15"
        android:text="佣金明细"
        android:textColor="@color/c_33"
        android:textSize="@dimen/sp15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewLineCommissionPlan"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#d7d7d7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textCommissionPlanTitle" />

    <LinearLayout
        android:id="@+id/linearCommissionDetails"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp40"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/skin_bg_promotion_commission_header"
        android:gravity="center"
        android:orientation="horizontal"
        android:weightSum="4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textCommissionPlanTitle">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="方案"
            android:textColor="@color/skin_color_1a"
            android:textSize="@dimen/sp12"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="兑换钻石(元)"
            android:textColor="@color/skin_color_1a"
            android:textSize="@dimen/sp12"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="上限"
            android:textColor="@color/skin_color_1a"
            android:textSize="@dimen/sp12"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="返点比例"
            android:textColor="@color/skin_color_1a"
            android:textSize="@dimen/sp12"
            android:textStyle="bold" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerPlanDetails"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginVertical="@dimen/dp8"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearCommissionDetails"
        tools:itemCount="3"
        tools:listitem="@layout/item_promotion_commission_details" />

</androidx.constraintlayout.widget.ConstraintLayout>
