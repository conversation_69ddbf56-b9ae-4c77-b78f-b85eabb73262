<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/skin_color_personal_info_main_background_white"
    tools:context=".activity.promotion.PromotionSuperiorsPageActivity">

    <include layout="@layout/view_title" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        style="@style/style_smart_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/flTitle">

        <include layout="@layout/layout_refresh_header" />

        <androidx.core.widget.NestedScrollView
            style="@style/style_recyclerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp15"
                    android:background="@drawable/skin_bg_superiors_radius_15"
                    android:minHeight="@dimen/size_160dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableStart="@drawable/ic_promotion_line"
                        android:drawablePadding="@dimen/dp4"
                        android:gravity="center|start"
                        android:padding="@dimen/dp15"
                        android:paddingVertical="@dimen/dp16"
                        android:text="我的下级默认分成"
                        android:textColor="@color/c_33"
                        android:textSize="@dimen/sp15" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:background="#d7d7d7" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/dp26"
                        android:paddingBottom="@dimen/dp16">

                        <TextView
                            android:id="@+id/text_live"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp20"
                            android:gravity="center|start"
                            android:text="直播佣金比例%"
                            android:textColor="@color/c_33"
                            android:textSize="@dimen/sp15"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <LinearLayout
                            android:id="@+id/linearLive"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp20"
                            android:gravity="center"
                            android:orientation="horizontal"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/text_live"
                            app:layout_constraintTop_toTopOf="parent">

                            <EditText
                                android:id="@+id/textLiveCommission"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@null"
                                android:gravity="center"
                                android:hint="请输入0-100的数字"
                                android:inputType="number"
                                android:maxLength="3"
                                android:maxLines="1"
                                android:textColor="@color/c_33"
                                android:textColorHint="@color/c_99"
                                android:textSize="@dimen/sp13" />

                            <ImageView
                                android:id="@+id/clearLiveCommission"
                                android:layout_width="@dimen/dp18"
                                android:layout_height="@dimen/dp18"
                                android:layout_marginStart="@dimen/dp5"
                                android:src="@drawable/ic_promotion_remove_text"
                                android:visibility="invisible" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/imageLiveCommission"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp20"
                            android:src="@drawable/ic_promotion_edit"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:layout_marginHorizontal="@dimen/dp20"
                        android:background="#d7d7d7" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/dp16"
                        android:paddingBottom="@dimen/dp30">

                        <TextView
                            android:id="@+id/text_game"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp20"
                            android:gravity="center|start"
                            android:text="游戏佣金比例%"
                            android:textColor="@color/c_33"
                            android:textSize="@dimen/sp15"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <LinearLayout
                            android:id="@+id/linearGame"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp20"
                            android:gravity="center"
                            android:orientation="horizontal"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/text_game"
                            app:layout_constraintTop_toTopOf="parent">

                            <EditText
                                android:id="@+id/textGameCommission"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@null"
                                android:gravity="center"
                                android:hint="请输入0-100的数字"
                                android:inputType="number"
                                android:maxLength="3"
                                android:maxLines="1"
                                android:textColor="@color/c_33"
                                android:textColorHint="@color/c_99"
                                android:textSize="@dimen/sp13" />

                            <ImageView
                                android:id="@+id/clearGameCommission"
                                android:layout_width="@dimen/dp18"
                                android:layout_height="@dimen/dp18"
                                android:layout_marginStart="@dimen/dp5"
                                android:src="@drawable/ic_promotion_remove_text"
                                android:visibility="invisible" />
                        </LinearLayout>


                        <ImageView
                            android:id="@+id/imageGameCommission"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp20"
                            android:src="@drawable/ic_promotion_edit"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp15"
                    android:background="@drawable/skin_bg_superiors_radius_15"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/linearHeaderMyInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="@dimen/dp47"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableStart="@drawable/ic_promotion_line"
                            android:drawablePadding="@dimen/dp4"
                            android:gravity="center|start"
                            android:padding="@dimen/dp15"
                            android:paddingVertical="@dimen/dp16"
                            android:text="我的名片"
                            android:textColor="@color/c_33"
                            android:textSize="@dimen/sp15" />

                        <ImageView
                            android:id="@+id/imageMineCopy"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/dp20"
                            android:src="@drawable/ic_promotion_copy" />
                    </RelativeLayout>

                    <View
                        android:id="@+id/viewLineMyInfo"
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:background="#d7d7d7"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearHeaderMyInfo" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/constraintEditProfile"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/viewLineMyInfo">

                        <com.makeramen.roundedimageview.RoundedImageView
                            android:id="@+id/imageMineAvatar"
                            android:layout_width="@dimen/size_60dp"
                            android:layout_height="@dimen/size_60dp"
                            android:layout_marginStart="@dimen/dp20"
                            android:layout_marginTop="@dimen/dp15"
                            android:adjustViewBounds="true"
                            android:importantForAccessibility="no"
                            android:scaleType="centerCrop"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            android:background="@drawable/skin_shape_personal_info_avatar_border"
                            app:riv_oval="true"
                            tools:src="@drawable/ph_live" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp15"
                            android:orientation="vertical"
                            app:layout_constraintBottom_toBottomOf="@id/imageMineAvatar"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/imageMineAvatar"
                            app:layout_constraintTop_toTopOf="@id/imageMineAvatar">

                            <TextView
                                android:id="@+id/textMineName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:singleLine="true"
                                android:textColor="@color/c_33"
                                android:textSize="@dimen/sp16"
                                android:textStyle="bold"
                                tools:text="挺糊的顺应" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp12"
                                android:layout_marginEnd="@dimen/dp24"
                                android:gravity="center_vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:text="签名："
                                    android:textSize="@dimen/sp12"
                                    android:textColor="@color/skin_color_number_id_label" />

                                <TextView
                                    android:id="@+id/tvMineSign"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp8"
                                    android:layout_marginEnd="@dimen/dp8"
                                    android:gravity="center|start"
                                    android:textColor="@color/c_33"
                                    android:maxLines="3"
                                    android:textSize="@dimen/font_13sp"
                                    tools:text="哦哦哦咯哦哟某物来咯路口这里面前天晚点圈里了啊险哈我在千嘛啊！" />
                            </LinearLayout>
                        </LinearLayout>

                        <FrameLayout
                            android:id="@+id/frameMine"
                            android:layout_width="@dimen/size_20dp"
                            android:layout_height="@dimen/size_20dp"
                            android:layout_marginEnd="@dimen/dp20"
                            app:layout_constraintBottom_toBottomOf="@id/imageMineAvatar"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/imageMineAvatar">

                            <ImageView
                                android:layout_width="@dimen/dp6"
                                android:layout_height="@dimen/dp18"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_promotion_arrow_right" />
                        </FrameLayout>

                        <LinearLayout
                            android:id="@+id/area_personal_info"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp20"
                            android:orientation="vertical"
                            android:layout_marginTop="@dimen/dp24"
                            android:paddingBottom="@dimen/dp22"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/imageMineAvatar"
                            android:background="@drawable/skin_shape_bg_area_personal_info_white">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <!-- 家乡 -->
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        style="@style/skin_personal_info_label_detail"
                                        android:text="ID" />

                                    <TextView
                                        android:id="@+id/textMineID"
                                        style="@style/skin_personal_info_value_detail"
                                        android:textColor="@color/c_33" />
                                </LinearLayout>
                                <!-- 年龄 -->
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        style="@style/skin_personal_info_label_detail"
                                        android:text="微信" />

                                    <TextView
                                        android:id="@+id/textMineWechat"
                                        style="@style/skin_personal_info_value_detail"
                                        android:textColor="@color/c_33" />
                                </LinearLayout>


                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="20dp">
                                <!-- 职业 -->
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        style="@style/skin_personal_info_label_detail"
                                        android:text="绑定码" />

                                    <TextView
                                        android:id="@+id/textMineCode"
                                        style="@style/skin_personal_info_value_detail"
                                        android:textColor="@color/c_33" />
                                </LinearLayout>


                                <!-- 感情 -->
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        style="@style/skin_personal_info_label_detail"
                                        android:text="电话" />

                                    <TextView
                                        android:id="@+id/textMinePhone"
                                        style="@style/skin_personal_info_value_detail"
                                        android:textColor="@color/c_33" />
                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp15"
                    android:background="@drawable/skin_bg_superiors_radius_15"
                    android:minHeight="@dimen/size_160dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/linearSuperiorInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="@dimen/dp47"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableStart="@drawable/ic_promotion_line"
                            android:drawablePadding="@dimen/dp4"
                            android:gravity="center|start"
                            android:padding="@dimen/dp15"
                            android:paddingVertical="@dimen/dp16"
                            android:text="我的上级"
                            android:textColor="@color/c_33"
                            android:textSize="@dimen/sp15" />

                        <ImageView
                            android:id="@+id/imageSuperiorCopy"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/dp20"
                            android:src="@drawable/ic_promotion_copy" />
                    </RelativeLayout>

                    <View
                        android:id="@+id/viewLineSuperiorInfo"
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:background="#d7d7d7"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearSuperiorInfo" />


                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/imageSuperiorAvatar"
                        android:layout_width="@dimen/size_60dp"
                        android:layout_height="@dimen/size_60dp"
                        android:layout_marginStart="@dimen/dp20"
                        android:layout_marginTop="@dimen/dp15"
                        android:adjustViewBounds="true"
                        android:importantForAccessibility="no"
                        android:scaleType="centerCrop"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/viewLineSuperiorInfo"
                        android:background="@drawable/skin_shape_personal_info_avatar_border"
                        app:riv_oval="true"
                        tools:src="@drawable/ph_live" />

                    <LinearLayout
                        android:id="@+id/linearSuperiorName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp15"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="@id/imageSuperiorAvatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/imageSuperiorAvatar"
                        app:layout_constraintTop_toTopOf="@id/imageSuperiorAvatar">

                        <TextView
                            android:id="@+id/textSuperiorName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:singleLine="true"
                            android:textColor="@color/c_33"
                            android:textSize="@dimen/sp16"
                            android:textStyle="bold"
                            tools:text="挺糊的顺应" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp12"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="签名："
                                android:textSize="@dimen/sp12"
                                android:textColor="@color/skin_color_number_id_label" />

                            <TextView
                                android:id="@+id/tvSuperiorSign"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp8"
                                android:layout_marginEnd="@dimen/dp8"
                                android:gravity="center|start"
                                android:textColor="@color/c_33"
                                android:textSize="@dimen/sp13"
                                tools:text="未设置" />
                        </LinearLayout>
                    </LinearLayout>

                    <FrameLayout
                        android:id="@+id/frameSuperior"
                        android:layout_width="@dimen/size_20dp"
                        android:layout_height="@dimen/size_20dp"
                        android:layout_marginEnd="@dimen/dp20"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/imageSuperiorAvatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/imageSuperiorAvatar">

                        <ImageView
                            android:layout_width="@dimen/dp6"
                            android:layout_height="@dimen/dp18"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_promotion_arrow_right" />
                    </FrameLayout>

                    <LinearLayout
                        android:id="@+id/area_superior_info"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp20"
                        android:orientation="vertical"
                        android:layout_marginTop="@dimen/dp32"
                        android:paddingBottom="@dimen/dp22"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/imageSuperiorAvatar"
                        android:background="@drawable/skin_shape_bg_area_personal_info_white">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <!-- 家乡 -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/skin_personal_info_label_detail"
                                    android:text="ID" />

                                <TextView
                                    android:id="@+id/textSuperiorID"
                                    style="@style/skin_personal_info_value_detail"
                                    android:textColor="@color/c_33" />
                            </LinearLayout>
                            <!-- 年龄 -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/skin_personal_info_label_detail"
                                    android:text="微信" />

                                <TextView
                                    android:id="@+id/textSuperiorWechat"
                                    style="@style/skin_personal_info_value_detail"
                                    android:textColor="@color/c_33" />
                            </LinearLayout>


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp">
                            <!-- 职业 -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/skin_personal_info_label_detail"
                                    android:text="绑定码" />

                                <TextView
                                    android:id="@+id/textSuperiorCode"
                                    style="@style/skin_personal_info_value_detail"
                                    android:textColor="@color/c_33" />
                            </LinearLayout>


                            <!-- 感情 -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/skin_personal_info_label_detail"
                                    android:text="电话" />

                                <TextView
                                    android:id="@+id/textSuperiorPhone"
                                    style="@style/skin_personal_info_value_detail"
                                    android:textColor="@color/c_33" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <TextView
                        android:id="@+id/textSuperiorEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:singleLine="true"
                        android:text="暂无上级"
                        android:textColor="@color/c_33"
                        android:textSize="@dimen/sp13"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/viewLineSuperiorInfo"
                        tools:text="挺糊的顺应" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp15"
                    android:layout_marginTop="@dimen/dp16"
                    android:layout_marginBottom="@dimen/dp70"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <Button
                        android:id="@+id/buttonCommission"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/size_6dp"
                        android:layout_weight="1"
                        android:background="@drawable/skin_bg_superiors_commission_btn"
                        android:text="下级佣金方案"
                        android:textAllCaps="false"
                        android:textColor="#11cfff" />

                    <Button
                        android:id="@+id/buttonSave"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/size_6dp"
                        android:layout_weight="1"
                        android:background="@drawable/skin_bg_superiors_save_btn"
                        android:text="保存分成"
                        android:textAllCaps="false"
                        android:textColor="#ffffff" />
                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
