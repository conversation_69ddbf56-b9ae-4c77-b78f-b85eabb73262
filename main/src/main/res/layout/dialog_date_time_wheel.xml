<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/skin_shape_white_top_corner_15"
    android:paddingVertical="@dimen/dp16">

    <LinearLayout
        android:id="@+id/lnHeader"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp8"
        android:orientation="horizontal"
        android:weightSum="3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingStart="@dimen/dp16"
            android:text="年"
            android:textColor="@color/skin_color_black_light"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingStart="@dimen/dp16"
            android:text="月"
            android:textColor="@color/skin_color_black_light"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingEnd="@dimen/dp16"
            android:text="日"
            android:textColor="@color/skin_color_black_light"
            android:textSize="16sp"
            android:textStyle="bold" />
    </LinearLayout>

    <com.github.gzuliyujiang.wheelpicker.widget.DateWheelLayout
        android:id="@+id/dateTimeWheel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lnHeader"
        app:wheel_curtainEnabled="true" />

    <Button
        android:id="@+id/btnCancel"
        android:layout_width="@dimen/dp140"
        android:layout_height="@dimen/dp40"
        android:layout_marginVertical="@dimen/dp16"
        android:background="@drawable/skin_shape_date_time_cancel_bg"
        android:gravity="center"
        android:text="取 消"
        android:textColor="@color/skin_color_date_time_dialog_text_cancel"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/btnOk"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dateTimeWheel" />

    <Button
        android:id="@+id/btnOk"
        android:layout_width="@dimen/dp140"
        android:layout_height="@dimen/dp40"
        android:layout_marginVertical="@dimen/dp16"
        android:background="@drawable/skin_shape_date_time_ok_bg"
        android:gravity="center"
        android:text="确 定"
        android:textColor="@color/skin_color_date_time_dialog_text_ok"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnCancel"
        app:layout_constraintTop_toBottomOf="@+id/dateTimeWheel" />

</androidx.constraintlayout.widget.ConstraintLayout>