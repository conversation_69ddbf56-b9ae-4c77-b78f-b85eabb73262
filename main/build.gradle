plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        minSdk rootProject.ext.android.minSdkVersion
        targetSdk rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
        buildConfigField 'String', 'VERSION_NAME', "\"${rootProject.ext.android.versionName}\""
        buildConfigField 'String', 'ZIFY_STT', "\"app.laksdfjlwex.com\""
    }
    aaptOptions {
        // Enable cruncher for better PNG optimization
        cruncherEnabled = true
        // useNewCruncher is deprecated and always enabled now
    }
    buildTypes {
        release {
            minifyEnabled true
            // Note: shrinkResources not available for library modules
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
    }
}
repositories {
    flatDir {
        dirs 'libs', '../libs'
    }
}
dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(include: ['*.aar'], dir: 'libs')
    api project(':skin')
    api project(':game')
    api project(':common')

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "androidx.constraintlayout:constraintlayout:2.1.4"
//    implementation 'com.android.support:design:28.0.0'
    implementation 'com.github.Justson:Downloader:v5.0.0-androidx'
    implementation 'com.google.android.material:material:1.8.0'// (可选)
    //阿里 ARouter
    implementation rootProject.ext.dependencies["arouter"]
    implementation 'androidx.core:core-ktx:1.8.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    kapt rootProject.ext.dependencies["arouter-compiler"]
    //国际区号
    implementation 'com.github.sahooz:country-picker-android:2.0.3'


    implementation 'io.reactivex.rxjava2:rxjava:2.2.19'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    implementation 'com.trello.rxlifecycle2:rxlifecycle-android-lifecycle:2.2.2'

    //如果“下载配置”中的下载地址配置的是应用的Google Play地址，则可以添加以下依赖，用于提升 Google Play 商店下载时参数还原精度
    implementation "com.android.installreferrer:installreferrer:2.2"
    //左右滑动
//    implementation 'com.github.wjianchen13:SlideDemo:1.0.0'
    //垂直
    implementation 'com.github.castorflex.verticalviewpager:library:19.0.1'

    implementation(name: 'netmobsecLib_4.4.6.8', ext: 'aar')

    implementation 'com.github.ctiao:DanmakuFlameMaster:0.9.25'
    implementation 'com.github.ctiao:ndkbitmap-armv7a:0.9.21'
    implementation "dev.chrisbanes.insetter:insetter:0.6.1"

    implementation 'com.github.princekin-f:EasyFloat:2.0.4'

    implementation 'com.github.getActivity:ShapeView:8.5'
    //直播间清屏
    implementation project(':slide_library')
    //礼物布局
    implementation project(path: ':giftlibrary')
    implementation project(':live_stream:core')

    implementation 'com.github.AAChartModel:AAChartCore-Kotlin:7.2.0'
    //vi币
    api project(':vi')
//    api 'cn.jiguang.sdk:jpush:4.8.5'  // 此处以JPush 4.8.5 版本为例。
//    api 'cn.jiguang.sdk:jcore:4.0.0'  // 此处以JCore 4.0.0 版本为例。

}